<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EclipseCodeFormatterProjectSettings">
    <option name="projectSpecificProfile">
      <ProjectSpecificProfile>
        <option name="formatOtherFileTypesWithIntelliJ" value="false" />
        <option name="formatter" value="ECLIPSE" />
        <option name="pathToConfigFileJava" value="$USER_HOME$/java_tools/plbiz_formatting_style.xml" />
        <option name="selectedJavaProfile" value="PLBIZ Style" />
      </ProjectSpecificProfile>
    </option>
    <option name="selectedGlobalProfile">
      <Settings>
        <option name="formatOtherFileTypesWithIntelliJ" value="false" />
        <option name="formatter" value="ECLIPSE" />
        <option name="id" value="1639622524600" />
        <option name="importOrderConfigFilePath" value="java;javax;org;com;" />
        <option name="name" value="Plbiz" />
        <option name="pathToConfigFileJava" value="$USER_HOME$/java_tools/plbiz_formatting_style_bak.xml" />
        <option name="selectedJavaProfile" value="PLBIZ Style" />
      </Settings>
    </option>
  </component>
</project>