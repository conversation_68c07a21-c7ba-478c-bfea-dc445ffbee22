/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    LoginModal: typeof import('./src/components/LoginModal.vue')['default']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
  }
}
