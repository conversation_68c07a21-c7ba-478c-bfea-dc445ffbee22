<template>
  <view class="theme-demo">
    <view class="demo-section">
      <text class="section-title">按钮组件主题演示</text>
      
      <view class="button-group">
        <nut-button type="primary" size="large">主要按钮 - 大</nut-button>
        <nut-button type="primary">主要按钮 - 默认</nut-button>
        <nut-button type="primary" size="small">主要按钮 - 小</nut-button>
        <nut-button type="primary" size="mini">主要按钮 - 迷你</nut-button>
      </view>

      <view class="button-group">
        <nut-button type="default">默认按钮</nut-button>
        <nut-button type="success">成功按钮</nut-button>
        <nut-button type="warning">警告按钮</nut-button>
        <nut-button type="danger">危险按钮</nut-button>
      </view>

      <view class="button-group">
        <nut-button type="primary" plain>朴素主要按钮</nut-button>
        <nut-button type="primary" disabled>禁用按钮</nut-button>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">弹窗组件主题演示</text>
      <nut-button type="primary" @click="showPopup = true">显示弹窗</nut-button>
      
      <nut-popup v-model="showPopup" position="center" :style="{ padding: '20px', borderRadius: '16px' }">
        <view class="popup-content">
          <text class="popup-title">主题化弹窗</text>
          <text class="popup-text">这是一个使用自定义主题的弹窗示例</text>
          <nut-button type="primary" size="small" @click="showPopup = false">关闭</nut-button>
        </view>
      </nut-popup>
    </view>

    <view class="demo-section">
      <text class="section-title">色彩系统演示</text>
      
      <view class="color-palette">
        <view class="color-item primary">
          <view class="color-block"></view>
          <text class="color-name">主色调</text>
          <text class="color-value">#8ECAE6</text>
        </view>
        
        <view class="color-item secondary">
          <view class="color-block"></view>
          <text class="color-name">辅助色</text>
          <text class="color-value">#F4F1DE</text>
        </view>
        
        <view class="color-item success">
          <view class="color-block"></view>
          <text class="color-name">成功色</text>
          <text class="color-value">#52C41A</text>
        </view>
        
        <view class="color-item warning">
          <view class="color-block"></view>
          <text class="color-name">警告色</text>
          <text class="color-value">#FFB84D</text>
        </view>
        
        <view class="color-item danger">
          <view class="color-block"></view>
          <text class="color-name">危险色</text>
          <text class="color-value">#FA5151</text>
        </view>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">字体系统演示</text>
      
      <view class="typography-demo">
        <text class="font-title">标题字体 - 20px Bold</text>
        <text class="font-heading">标题字体 - 18px SemiBold</text>
        <text class="font-body">正文字体 - 16px Regular</text>
        <text class="font-caption">说明字体 - 12px Light</text>
        <text class="font-number">数字字体 - 14px Bold</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showPopup = ref(false)
</script>

<style lang="scss" scoped>
.theme-demo {
  padding: var(--spacing-md);
  background-color: var(--bg-gray);
  min-height: 100vh;
}

.demo-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.popup-content {
  text-align: center;
  
  .popup-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  .popup-text {
    display: block;
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
  }
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.color-item {
  text-align: center;
  
  .color-block {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-medium);
    margin: 0 auto var(--spacing-sm);
    box-shadow: var(--shadow-light);
  }
  
  .color-name {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
  }
  
  .color-value {
    display: block;
    font-size: 10px;
    color: var(--text-secondary);
    font-family: monospace;
  }
  
  &.primary .color-block {
    background-color: var(--primary-color);
  }
  
  &.secondary .color-block {
    background-color: var(--secondary-color);
  }
  
  &.success .color-block {
    background-color: var(--success-color);
  }
  
  &.warning .color-block {
    background-color: var(--warning-color);
  }
  
  &.danger .color-block {
    background-color: var(--danger-color);
  }
}

.typography-demo {
  .font-title,
  .font-heading,
  .font-body,
  .font-caption,
  .font-number {
    display: block;
    margin-bottom: var(--spacing-sm);
  }
  
  .font-title {
    @extend .font-title;
  }
  
  .font-heading {
    @extend .font-heading;
  }
  
  .font-body {
    @extend .font-body;
  }
  
  .font-caption {
    @extend .font-caption;
  }
  
  .font-number {
    @extend .font-number;
  }
}
</style>
