<template>
  <nut-popup
    v-model:visible="visible"
    position="center"
    :closeable="false"
    :close-on-click-overlay="false"
    round
    :style="{ width: '300px' }"
  >
    <div class="login-modal">
      <!-- 头部 -->
      <div class="login-header">
        <div class="logo">
          <div style="font-size: 48px; color: #8ECAE6;">😊</div>
        </div>
        <h2 class="title">今日碎语</h2>
        <p class="subtitle">记录每一刻的情绪变化</p>
      </div>

      <!-- 登录状态显示 -->
      <div class="login-content">
        <!-- 加载状态 -->
        <div v-if="userStore.isLoggingIn" class="loading-state">
          <div class="loading-spinner">⏳</div>
          <p class="loading-text">正在登录中...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="userStore.hasLoginError" class="error-state">
          <div style="font-size: 32px; color: #FA5151;">⚠️</div>
          <p class="error-text">{{ userStore.loginError }}</p>
          <nut-button
            type="primary"
            size="small"
            @click="retryLogin"
            :loading="userStore.isLoggingIn"
          >
            重试
          </nut-button>
        </div>

        <!-- 登录按钮 -->
        <div v-else class="login-actions">
          <nut-button
            type="primary"
            size="large"
            block
            @click="handleWeChatLogin"
            :loading="userStore.isLoggingIn"
            :disabled="userStore.isLoggingIn"
          >
            <view style="font-size: 18px; margin-right: 8px; display: inline-block;">💬</view>
            微信授权登录
          </nut-button>

          <p class="login-tips">
            登录即表示同意
            <span class="link" @click="showPrivacyPolicy">《隐私政策》</span>
            和
            <span class="link" @click="showUserAgreement">《用户协议》</span>
          </p>
        </div>
      </div>
    </div>
  </nut-popup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '../store/user'
import Taro from '@tarojs/taro'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'loginSuccess'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => {
    emit('update:modelValue', value)
  }
})

// 微信登录处理
const handleWeChatLogin = async () => {
  try {
    const success = await userStore.weChatLogin(true)
    if (success) {
      visible.value = false
      emit('loginSuccess')

      // 显示登录成功提示
      Taro.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('登录处理失败:', error)
  }
}

// 重试登录
const retryLogin = () => {
  userStore.clearLoginError()
  handleWeChatLogin()
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  Taro.showModal({
    title: '隐私政策',
    content: '我们重视您的隐私，仅收集必要的用户信息用于提供服务。详细内容请查看完整版隐私政策。',
    showCancel: false,
    confirmText: '知道了'
  })
}

// 显示用户协议
const showUserAgreement = () => {
  Taro.showModal({
    title: '用户协议',
    content: '使用本应用即表示您同意遵守相关服务条款。详细内容请查看完整版用户协议。',
    showCancel: false,
    confirmText: '知道了'
  })
}

onMounted(() => {
  console.log('LoginModal 组件已挂载')
})
</script>

<style lang="scss" scoped>
.login-modal {
  padding: 32px 24px;
  text-align: center;
  background: var(--bg-primary);
  border-radius: 16px;
}

.login-header {
  margin-bottom: 32px;

  .logo {
    margin-bottom: 16px;
  }

  .title {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }

  .subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
  }
}

.login-content {
  .loading-state {
    padding: 24px 0;

    .loading-spinner {
      font-size: 32px;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-top: 16px;
      font-size: 14px;
      color: var(--text-secondary);
    }
  }

  .error-state {
    padding: 24px 0;

    .error-text {
      margin: 16px 0;
      font-size: 14px;
      color: var(--danger-color);
      line-height: 1.4;
    }
  }

  .login-actions {
    .login-tips {
      margin-top: 16px;
      font-size: 12px;
      color: var(--text-light);
      line-height: 1.4;

      .link {
        color: var(--primary-color);
        text-decoration: underline;
        cursor: pointer;

        &:hover {
          color: var(--primary-dark);
        }
      }
    }
  }
}

// NutUI 组件样式覆盖 - 小程序环境优化
:deep(.nut-popup) {
  .nut-popup__content {
    border-radius: var(--radius-large) !important;
    overflow: hidden;
    max-width: 90vw;
    max-height: 80vh;
  }

  .nut-popup__close-icon {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

:deep(.nut-button--primary) {
  // 确保主题色正确应用
  background: var(--nut-button-primary-background-color, linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%)) !important;
  border-color: var(--nut-button-primary-border-color, var(--primary-color)) !important;
  color: var(--nut-button-primary-color, var(--text-white)) !important;

  // 小程序环境下的触摸反馈
  &:active {
    background: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    opacity: 0.9;
    transform: translateY(1px);
  }
}

// 动画定义
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
