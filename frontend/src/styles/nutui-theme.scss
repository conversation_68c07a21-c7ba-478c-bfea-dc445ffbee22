/**
 * NutUI 主题定制配置
 * 基于 Today's Murmur 设计规范的 NutUI 组件主题变量覆盖
 */

// ==================== CSS 自定义属性覆盖 ====================
// 通过 CSS 自定义属性覆盖 NutUI 的主题变量
:root {
  // 主色调
  --nut-primary-color: #8ECAE6;
  --nut-primary-color-end: #A8D8EA;

  // 辅助色
  --nut-help-color: #F4F1DE;
  --nut-white: #FFFFFF;
  --nut-black: #2A2A2A;

  // 文本色
  --nut-title-color: #2A2A2A;
  --nut-title-color2: #888888;
  --nut-text-color: #CCCCCC;
  --nut-disable-color: #EAEAEA;
  --nut-required-color: #FA5151;

  // 字体
  --nut-font-family: 'PingFang SC', 'Roboto', 'Noto Sans', 'Microsoft YaHei', 'Helvetica', 'Hiragino Sans GB', 'SimSun', sans-serif;
  --nut-font-size-0: 10px;
  --nut-font-size-1: 12px;
  --nut-font-size-2: 14px;
  --nut-font-size-3: 16px;
  --nut-font-size-4: 18px;
  --nut-font-weight-bold: 500;
  --nut-line-height-base: 1.5;

  // 按钮
  --nut-button-border-radius: 8px;
  --nut-button-default-height: 40px;
  --nut-button-large-height: 48px;
  --nut-button-small-height: 32px;
  --nut-button-mini-height: 28px;
  --nut-button-default-padding: 0 20px;
  --nut-button-small-padding: 0 16px;
  --nut-button-mini-padding: 0 12px;
  --nut-button-default-font-size: 14px;
  --nut-button-large-font-size: 16px;
  --nut-button-small-font-size: 12px;
  --nut-button-primary-color: #FFFFFF;
  --nut-button-primary-border-color: #8ECAE6;
  --nut-button-primary-background-color: linear-gradient(135deg, #8ECAE6 0%, #A8D8EA 100%);
  --nut-button-disabled-opacity: 0.6;

  // 弹窗
  --nut-popup-border-radius: 16px;
  --nut-popup-close-icon-margin: 16px;
  --nut-overlay-bg-color: rgba(0, 0, 0, 0.5);

  // 单元格
  --nut-cell-border-radius: 8px;
  --nut-cell-padding: 16px;
  --nut-cell-background: #FFFFFF;
  --nut-cell-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  // 动画
  --nut-animation-duration: 0.3s;
  --nut-animation-timing-fun: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// ==================== 色彩系统 ====================

// 主色调 - 使用项目的主色调 #8ECAE6
$primary-color: #8ECAE6 !default;
$primary-color-end: #A8D8EA !default;

// 辅助色系
$help-color: #F4F1DE !default;
$white: #FFFFFF !default;
$black: #2A2A2A !default;

// 文本色系
$title-color: #2A2A2A !default;
$title-color2: #888888 !default;
$text-color: #CCCCCC !default;
$disable-color: #EAEAEA !default;
$required-color: #FA5151 !default;

// 功能色系
$success-color: #52C41A !default;
$warning-color: #FFB84D !default;
$danger-color: #FA5151 !default;
$info-color: #1890FF !default;

// ==================== 字体系统 ====================

$font-family: 'PingFang SC', 'Roboto', 'Noto Sans', 'Microsoft YaHei', 'Helvetica', 'Hiragino Sans GB', 'SimSun', sans-serif !default;

// 字体大小
$font-size-0: 10px !default;
$font-size-1: 12px !default;
$font-size-2: 14px !default;
$font-size-3: 16px !default;
$font-size-4: 18px !default;
$font-weight-bold: 500 !default;

$font-size-small: $font-size-1 !default;
$font-size-base: $font-size-2 !default;
$font-size-large: $font-size-3 !default;
$line-height-base: 1.5 !default;

// ==================== 按钮组件主题 ====================

// 按钮圆角 - 使用更现代的圆角设计
$button-border-radius: 8px !default;
$button-border-width: 1px !default;

// 按钮尺寸
$button-default-height: 40px !default;
$button-large-height: 48px !default;
$button-small-height: 32px !default;
$button-mini-height: 28px !default;

// 按钮内边距
$button-default-padding: 0 20px !default;
$button-small-padding: 0 16px !default;
$button-mini-padding: 0 12px !default;

// 按钮字体
$button-default-font-size: $font-size-2 !default;
$button-large-font-size: $font-size-3 !default;
$button-small-font-size: $font-size-1 !default;
$button-mini-font-size: $font-size-1 !default;

// 主要按钮样式
$button-primary-color: $white !default;
$button-primary-border-color: $primary-color !default;
$button-primary-background-color: linear-gradient(135deg, $primary-color 0%, $primary-color-end 100%) !default;

// 默认按钮样式
$button-default-bg-color: $white !default;
$button-default-border-color: #EAEAEA !default;
$button-default-color: $title-color2 !default;

// 成功按钮样式
$button-success-color: $white !default;
$button-success-border-color: $success-color !default;
$button-success-background-color: linear-gradient(135deg, $success-color 0%, #6BD63A 100%) !default;

// 危险按钮样式
$button-danger-color: $white !default;
$button-danger-border-color: $danger-color !default;
$button-danger-background-color: $danger-color !default;

// 警告按钮样式
$button-warning-color: $white !default;
$button-warning-border-color: $warning-color !default;
$button-warning-background-color: linear-gradient(135deg, $warning-color 0%, #FFD166 100%) !default;

// 信息按钮样式
$button-info-color: $white !default;
$button-info-border-color: $info-color !default;
$button-info-background-color: linear-gradient(135deg, $info-color 0%, #40A9FF 100%) !default;

// 朴素按钮样式
$button-plain-background-color: $white !default;

// 按钮禁用状态
$button-disabled-opacity: 0.6 !default;

// ==================== 单元格组件主题 ====================

$cell-color: $title-color2 !default;
$cell-title-font: $font-size-3 !default;
$cell-title-desc-font: $font-size-1 !default;
$cell-desc-font: $font-size-2 !default;
$cell-desc-color: $text-color !default;
$cell-border-radius: 8px !default;
$cell-padding: 16px !default;
$cell-line-height: 24px !default;
$cell-background: $white !default;
$cell-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !default;

// ==================== 弹窗组件主题 ====================

$popup-border-radius: 16px !default;
$popup-close-icon-margin: 16px !default;

// 遮罩层
$overlay-bg-color: rgba(0, 0, 0, 0.5) !default;

// ==================== 通知组件主题 ====================

$notify-text-color: $white !default;
$notify-padding: 12px 16px !default;
$notify-font-size: $font-size-2 !default;
$notify-height: 48px !default;
$notify-line-height: 1.5 !default;

// 通知背景色
$notify-base-background-color: linear-gradient(135deg, $primary-color 0%, $primary-color-end 100%) !default;
$notify-success-background-color: linear-gradient(135deg, $success-color 0%, #6BD63A 100%) !default;
$notify-danger-background-color: $danger-color !default;
$notify-warning-background-color: linear-gradient(135deg, $warning-color 0%, #FFD166 100%) !default;

// ==================== 输入框组件主题 ====================

$input-border-bottom: #F0F0F0 !default;
$input-disabled-color: $disable-color !default;
$input-required-color: $required-color !default;
$input-font-size: $font-size-2 !default;

// ==================== 文本域组件主题 ====================

$textarea-font: $font-size-2 !default;
$textarea-limit-color: $text-color !default;
$textarea-text-color: $title-color !default;
$textarea-disabled-color: $disable-color !default;

// ==================== 标签页组件主题 ====================

$tabs-tab-smile-color: $primary-color !default;
$tabs-titles-border-radius: 8px !default;
$tabs-titles-item-large-font-size: $font-size-3 !default;
$tabs-titles-item-font-size: $font-size-2 !default;
$tabs-titles-item-small-font-size: $font-size-1 !default;
$tabs-titles-item-color: $title-color !default;
$tabs-titles-item-active-color: $primary-color !default;
$tabs-titles-background-color: $help-color !default;

// 水平标签页
$tabs-horizontal-tab-line-color: linear-gradient(90deg, $primary-color 0%, rgba($primary-color, 0.3) 100%) !default;
$tabs-horizontal-titles-height: 48px !default;
$tabs-horizontal-titles-item-min-width: 60px !default;
$tabs-horizontal-titles-item-active-line-width: 40px !default;

// 垂直标签页
$tabs-vertical-tab-line-color: linear-gradient(180deg, $primary-color 0%, rgba($primary-color, 0.3) 100%) !default;
$tabs-vertical-titles-item-height: 44px !default;
$tabs-vertical-titles-item-active-line-height: 16px !default;
$tabs-vertical-titles-width: 120px !default;

// 标签页线条样式
$tabs-titles-item-line-border-radius: 2px !default;
$tabs-titles-item-line-opacity: 1 !default;

// ==================== 进度条组件主题 ====================

$progress-inner-background-color: linear-gradient(135deg, $primary-color 0%, $primary-color-end 100%) !default;
$progress-outer-background-color: #F8F9FA !default;
$progress-outer-border-radius: 8px !default;
$progress-insidetext-border-radius: 4px !default;
$progress-insidetext-padding: 4px 8px !default;

// 不同尺寸的进度条
$progress-small-height: 6px !default;
$progress-base-height: 8px !default;
$progress-large-height: 12px !default;

// ==================== 评分组件主题 ====================

$rate-icon-color: $primary-color !default;
$rate-icon-void-color: $disable-color !default;

// ==================== 徽标组件主题 ====================

$badge-background-color: linear-gradient(135deg, $primary-color 0%, $primary-color-end 100%) !default;
$badge-color: $white !default;
$badge-font-size: $font-size-1 !default;
$badge-border-radius: 12px !default;
$badge-padding: 2px 6px !default;

// 徽标点样式
$badge-dot-width: 8px !default;
$badge-dot-height: 8px !default;
$badge-dot-border-radius: 50% !default;

// ==================== 开关组件主题 ====================

$switch-close-bg-color: #F0F0F0 !default;
$switch-width: 44px !default;
$switch-height: 24px !default;
$switch-border-radius: 24px !default;
$switch-inside-width: 20px !default;
$switch-inside-height: 20px !default;

// ==================== 日历组件主题 ====================

$calendar-primary-color: $primary-color !default;
$calendar-choose-color: $primary-color !default;
$calendar-choose-background-color: rgba($primary-color, 0.1) !default;
$calendar-choose-font-color: $primary-color !default;
$calendar-base-color: $title-color !default;
$calendar-disable-color: $disable-color !default;

// 日历字体
$calendar-base-font: $font-size-3 !default;
$calendar-title-font: $font-size-4 !default;
$calendar-title-font-weight: 600 !default;
$calendar-sub-title-font: $font-size-2 !default;
$calendar-text-font: $font-size-1 !default;
$calendar-day-font: $font-size-3 !default;
$calendar-day-font-weight: 500 !default;

// ==================== 动画配置 ====================

$animation-duration: 0.3s !default;
$animation-timing-fun: cubic-bezier(0.25, 0.46, 0.45, 0.94) !default;
