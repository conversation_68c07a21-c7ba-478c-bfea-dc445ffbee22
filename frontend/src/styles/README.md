# NutUI 主题定制说明

## 概述

本项目基于 Today's Murmur 设计规范，对 NutUI 组件库进行了全面的主题定制。主题系统采用了 CSS 自定义属性（CSS Variables）和 Sass 变量相结合的方式，确保主题的一致性和可维护性。

## 文件结构

```
src/styles/
├── nutui-theme.scss    # NutUI 主题变量定义
└── README.md          # 本说明文档
```

## 主题配置方式

### 1. CSS 自定义属性覆盖

通过 CSS 自定义属性覆盖 NutUI 的默认主题变量，这是推荐的方式：

```scss
:root {
  --nut-primary-color: #8ECAE6;
  --nut-primary-color-end: #A8D8EA;
  --nut-button-border-radius: 8px;
  // ... 更多变量
}
```

### 2. Sass 变量覆盖

为了兼容性和构建时优化，同时提供 Sass 变量覆盖：

```scss
$primary-color: #8ECAE6 !default;
$primary-color-end: #A8D8EA !default;
$button-border-radius: 8px !default;
// ... 更多变量
```

## 主题色彩系统

### 主色调
- **主色**: `#8ECAE6` - 清新的天蓝色，体现平静和舒缓的情绪
- **主色渐变终点**: `#A8D8EA` - 更浅的蓝色，用于渐变效果

### 辅助色
- **辅助色**: `#F4F1DE` - 温暖的米色，用于背景和辅助元素
- **白色**: `#FFFFFF` - 纯白色
- **黑色**: `#2A2A2A` - 深灰色，替代纯黑色以减少视觉疲劳

### 文本色系
- **主要文本**: `#2A2A2A` - 深灰色，确保良好的可读性
- **次要文本**: `#888888` - 中灰色，用于辅助信息
- **浅色文本**: `#CCCCCC` - 浅灰色，用于占位符等
- **禁用色**: `#EAEAEA` - 浅灰色，用于禁用状态
- **必填色**: `#FA5151` - 红色，用于必填项标识

### 功能色系
- **成功色**: `#52C41A` - 绿色
- **警告色**: `#FFB84D` - 橙色
- **危险色**: `#FA5151` - 红色
- **信息色**: `#1890FF` - 蓝色

## 组件主题定制

### 按钮组件
- **圆角**: 8px - 现代化的圆角设计
- **高度**: 默认 40px，大按钮 48px，小按钮 32px
- **内边距**: 根据按钮大小调整
- **主要按钮**: 使用主色调渐变背景

### 弹窗组件
- **圆角**: 16px - 更大的圆角增加亲和力
- **遮罩**: 50% 透明度的黑色

### 单元格组件
- **圆角**: 8px
- **内边距**: 16px
- **阴影**: 轻微的阴影效果增加层次感

## 使用方法

### 1. 导入主题

主题已经在 `app.scss` 中自动导入：

```scss
@import './styles/nutui-theme.scss';
@import '@nutui/nutui-taro/dist/style.css';
```

### 2. 使用组件

直接使用 NutUI 组件，主题会自动应用：

```vue
<template>
  <nut-button type="primary" size="large">
    主要按钮
  </nut-button>
</template>
```

### 3. 自定义样式

如需额外的样式定制，使用 `:deep()` 选择器：

```scss
:deep(.nut-button--primary) {
  &:hover {
    background: var(--primary-dark);
  }
}
```

## 主题变量参考

### 常用变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `--nut-primary-color` | `#8ECAE6` | 主色调 |
| `--nut-primary-color-end` | `#A8D8EA` | 主色调渐变终点 |
| `--nut-button-border-radius` | `8px` | 按钮圆角 |
| `--nut-button-large-height` | `48px` | 大按钮高度 |
| `--nut-popup-border-radius` | `16px` | 弹窗圆角 |

### 完整变量列表

详细的变量列表请参考 `nutui-theme.scss` 文件中的定义。

## 最佳实践

1. **优先使用主题变量**: 尽量通过主题变量控制样式，避免硬编码颜色值
2. **保持一致性**: 所有组件应使用统一的主题变量
3. **响应式设计**: 确保主题在不同设备上的显示效果
4. **可访问性**: 确保颜色对比度符合无障碍访问标准

## 扩展主题

如需添加新的主题变量：

1. 在 `nutui-theme.scss` 中添加 CSS 自定义属性
2. 同时添加对应的 Sass 变量（如果需要）
3. 更新本文档的变量参考表

## 注意事项

1. **构建顺序**: 主题变量必须在 NutUI 样式之前导入
2. **变量命名**: 遵循 NutUI 的变量命名规范（`--nut-` 前缀）
3. **兼容性**: CSS 自定义属性在现代浏览器中支持良好
4. **性能**: 主题变量的修改会触发样式重新计算，避免频繁修改
