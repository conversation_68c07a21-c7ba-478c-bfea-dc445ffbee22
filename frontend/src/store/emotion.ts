import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Taro from '@tarojs/taro'

export interface Emotion {
  emotionId: number
  emotionLabel: string
  emotionIcon: string
  emotionColor: string
  isCustom: boolean
  userId?: string
  sortOrder?: number
}

export interface Cause {
  causeId: number
  causeLabel: string
  causeCategory: string
  isCustom: boolean
  userId?: string
  sortOrder?: number
}

export const useEmotionStore = defineStore('emotion', () => {
  // 状态
  const emotions = ref<Emotion[]>([])
  const causes = ref<Cause[]>([])
  const customEmotions = ref<Emotion[]>([])
  const loading = ref<boolean>(false)

  // 计算属性
  const systemEmotions = computed(() => {
    return emotions.value.filter(emotion => !emotion.isCustom)
  })

  const userEmotions = computed(() => {
    return emotions.value.filter(emotion => emotion.isCustom)
  })

  const causeCategories = computed(() => {
    const categories = new Set(causes.value.map(cause => cause.causeCategory))
    return Array.from(categories)
  })

  const getCausesByCategory = computed(() => {
    return (category: string) => {
      return causes.value.filter(cause => cause.causeCategory === category)
    }
  })

  // 方法
  const fetchEmotions = async () => {
    loading.value = true
    try {
      const response = await Taro.request({
        url: '/api/emotions',
        method: 'GET'
      })

      if (response.data.success) {
        emotions.value = response.data.data
      }
    } catch (error) {
      console.error('获取情绪列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchCauses = async () => {
    loading.value = true
    try {
      const response = await Taro.request({
        url: '/api/causes',
        method: 'GET'
      })

      if (response.data.success) {
        causes.value = response.data.data
      }
    } catch (error) {
      console.error('获取原因列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const addCustomEmotion = async (emotion: Omit<Emotion, 'emotionId'>) => {
    try {
      const response = await Taro.request({
        url: '/api/emotions',
        method: 'POST',
        data: emotion
      })

      if (response.data.success) {
        const newEmotion = response.data.data
        emotions.value.push(newEmotion)
        customEmotions.value.push(newEmotion)
        return newEmotion
      }
      return null
    } catch (error) {
      console.error('添加自定义情绪失败:', error)
      return null
    }
  }

  const getEmotionById = (id: number): Emotion | undefined => {
    return emotions.value.find(emotion => emotion.emotionId === id)
  }

  const getCauseById = (id: number): Cause | undefined => {
    return causes.value.find(cause => cause.causeId === id)
  }

  const initializeData = async () => {
    await Promise.all([
      fetchEmotions(),
      fetchCauses()
    ])
  }

  return {
    // 状态
    emotions,
    causes,
    customEmotions,
    loading,

    // 计算属性
    systemEmotions,
    userEmotions,
    causeCategories,
    getCausesByCategory,

    // 方法
    fetchEmotions,
    fetchCauses,
    addCustomEmotion,
    getEmotionById,
    getCauseById,
    initializeData
  }
})
