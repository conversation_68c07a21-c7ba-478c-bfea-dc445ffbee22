import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Taro from '@tarojs/taro'
import AuthService, { type WeChatLoginResponse } from '../services/auth'

export interface UserInfo {
  userId: string
  openId: string
  unionId?: string
  username: string
  nickname: string
  avatarUrl?: string
  lastLoginTime: string
  createdAt: string
}

// 登录状态枚举
export enum LoginStatus {
  LOGGED_OUT = 'logged_out',
  LOGGING_IN = 'logging_in',
  LOGGED_IN = 'logged_in',
  LOGIN_FAILED = 'login_failed'
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const isLoggedIn = ref<boolean>(false)
  const loginStatus = ref<LoginStatus>(LoginStatus.LOGGED_OUT)
  const loginError = ref<string>('')

  // 计算属性
  const displayName = computed(() => {
    return userInfo.value?.nickname || userInfo.value?.username || '未登录用户'
  })

  const avatar = computed(() => {
    return userInfo.value?.avatarUrl || '/assets/images/default-avatar.png'
  })

  const isLoggingIn = computed(() => {
    return loginStatus.value === LoginStatus.LOGGING_IN
  })

  const hasLoginError = computed(() => {
    return loginStatus.value === LoginStatus.LOGIN_FAILED && loginError.value !== ''
  })

  // 方法
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    isLoggedIn.value = true
  }

  const setToken = (newToken: string) => {
    token.value = newToken
    // 持久化存储
    Taro.setStorageSync('token', newToken)
  }

  const setLoginStatus = (status: LoginStatus, error: string = '') => {
    loginStatus.value = status
    loginError.value = error
  }

  const clearLoginError = () => {
    loginError.value = ''
    if (loginStatus.value === LoginStatus.LOGIN_FAILED) {
      loginStatus.value = LoginStatus.LOGGED_OUT
    }
  }

  /**
   * 微信授权登录
   */
  const weChatLogin = async (withUserInfo: boolean = true): Promise<boolean> => {
    try {
      setLoginStatus(LoginStatus.LOGGING_IN)
      clearLoginError()

      console.log('开始微信登录流程...')

      // 调用微信登录服务
      const loginResponse: WeChatLoginResponse = await AuthService.performWeChatLogin(withUserInfo)

      console.log('微信登录成功:', loginResponse)

      // 保存token
      setToken(loginResponse.data.token)

      // 保存用户信息
      if (loginResponse.data.user) {
        setUserInfo(loginResponse.data.user)
      } else {
        // 如果登录响应中没有完整用户信息，尝试获取
        try {
          const userProfile = await AuthService.getUserInfo(loginResponse.data.token)
          setUserInfo(userProfile.data)
        } catch (error) {
          console.warn('获取用户详细信息失败:', error)
          // 使用登录响应中的基本信息
          const basicUserInfo: UserInfo = {
            userId: loginResponse.data.userId,
            openId: '',
            username: loginResponse.data.nickname,
            nickname: loginResponse.data.nickname,
            avatarUrl: '',
            lastLoginTime: new Date().toISOString(),
            createdAt: new Date().toISOString()
          }
          setUserInfo(basicUserInfo)
        }
      }

      // 持久化用户信息
      Taro.setStorageSync('userInfo', userInfo.value)

      // 更新状态
      isLoggedIn.value = true
      setLoginStatus(LoginStatus.LOGGED_IN)

      console.log('微信登录流程完成')
      return true

    } catch (error) {
      console.error('微信登录失败:', error)
      const errorMessage = error instanceof Error ? error.message : '登录失败，请重试'
      setLoginStatus(LoginStatus.LOGIN_FAILED, errorMessage)

      // 显示错误提示
      Taro.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })

      return false
    }
  }

  const logout = () => {
    userInfo.value = null
    token.value = ''
    isLoggedIn.value = false
    setLoginStatus(LoginStatus.LOGGED_OUT)
    clearLoginError()

    // 清除本地存储
    Taro.removeStorageSync('token')
    Taro.removeStorageSync('userInfo')

    console.log('用户已退出登录')
  }

  const updateProfile = async (updates: Partial<UserInfo>) => {
    if (!userInfo.value) return false

    try {
      const response = await Taro.request({
        url: '/api/user/profile',
        method: 'PUT',
        data: updates,
        header: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (response.data.success) {
        userInfo.value = { ...userInfo.value, ...updates }
        return true
      }
      return false
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    }
  }

  /**
   * 从本地存储初始化用户状态
   */
  const initializeFromStorage = async () => {
    try {
      const storedToken = Taro.getStorageSync('token')
      const storedUserInfo = Taro.getStorageSync('userInfo')

      if (storedToken && storedUserInfo) {
        console.log('从本地存储恢复用户状态')

        // 验证token有效性
        const isValidToken = await AuthService.validateToken(storedToken)

        if (isValidToken) {
          token.value = storedToken
          userInfo.value = storedUserInfo
          isLoggedIn.value = true
          setLoginStatus(LoginStatus.LOGGED_IN)
          console.log('自动登录成功')
        } else {
          console.log('Token已过期，清除本地存储')
          logout()
        }
      } else {
        console.log('未找到本地登录信息')
        setLoginStatus(LoginStatus.LOGGED_OUT)
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error)
      logout()
    }
  }

  /**
   * 检查登录状态
   */
  const checkLoginStatus = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }

    try {
      const isValid = await AuthService.validateToken(token.value)
      if (!isValid) {
        logout()
        return false
      }
      return true
    } catch (error) {
      console.error('检查登录状态失败:', error)
      logout()
      return false
    }
  }

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,
    loginStatus,
    loginError,

    // 计算属性
    displayName,
    avatar,
    isLoggingIn,
    hasLoginError,

    // 方法
    setUserInfo,
    setToken,
    setLoginStatus,
    clearLoginError,
    weChatLogin,
    logout,
    updateProfile,
    initializeFromStorage,
    checkLoginStatus
  }
})
