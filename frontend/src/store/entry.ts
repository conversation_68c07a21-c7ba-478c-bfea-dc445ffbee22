import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Taro from '@tarojs/taro'
import type { Emotion, Cause } from './emotion'

export interface Entry {
  entryId: number
  userId: string
  date: string
  notes: string
  causeId: number
  emotionId: number
  intensity: number
  createdAt: string
  updatedAt: string
  emotion?: Emotion
  cause?: Cause
}

export interface Statistics {
  totalEntries: number
  emotionDistribution: { [key: string]: number }
  causeDistribution: { [key: string]: number }
  averageIntensity: number
  mostFrequentEmotion: string
  mostFrequentCause: string
}

export const useEntryStore = defineStore('entry', () => {
  // 状态
  const entries = ref<Entry[]>([])
  const todayEntries = ref<Entry[]>([])
  const statistics = ref<Statistics | null>(null)
  const loading = ref<boolean>(false)

  // 计算属性
  const latestEntry = computed(() => {
    if (todayEntries.value.length === 0) return null
    return todayEntries.value[todayEntries.value.length - 1]
  })

  const todayCount = computed(() => {
    return todayEntries.value.length
  })

  const getEntriesByDate = computed(() => {
    return (date: string) => {
      return entries.value.filter(entry =>
        entry.date.startsWith(date)
      )
    }
  })

  const getEntriesByEmotion = computed(() => {
    return (emotionId: number) => {
      return entries.value.filter(entry => entry.emotionId === emotionId)
    }
  })

  // 方法
  const fetchEntries = async (page = 1, limit = 20) => {
    loading.value = true
    try {
      const response = await Taro.request({
        url: '/api/entries',
        method: 'GET',
        data: { page, limit }
      })

      if (response.data.success) {
        if (page === 1) {
          entries.value = response.data.data.content
        } else {
          entries.value.push(...response.data.data.content)
        }
        updateTodayEntries()
      }
    } catch (error) {
      console.error('获取记录列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const createEntry = async (entryData: Omit<Entry, 'entryId' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await Taro.request({
        url: '/api/entries',
        method: 'POST',
        data: entryData
      })

      if (response.data.success) {
        const newEntry = response.data.data
        entries.value.unshift(newEntry)
        updateTodayEntries()
        return newEntry
      }
      return null
    } catch (error) {
      console.error('创建记录失败:', error)
      return null
    }
  }

  const updateEntry = async (entryId: number, updates: Partial<Entry>) => {
    try {
      const response = await Taro.request({
        url: `/api/entries/${entryId}`,
        method: 'PUT',
        data: updates
      })

      if (response.data.success) {
        const index = entries.value.findIndex(entry => entry.entryId === entryId)
        if (index !== -1) {
          entries.value[index] = { ...entries.value[index], ...updates }
          updateTodayEntries()
        }
        return true
      }
      return false
    } catch (error) {
      console.error('更新记录失败:', error)
      return false
    }
  }

  const deleteEntry = async (entryId: number) => {
    try {
      const response = await Taro.request({
        url: `/api/entries/${entryId}`,
        method: 'DELETE'
      })

      if (response.data.success) {
        entries.value = entries.value.filter(entry => entry.entryId !== entryId)
        updateTodayEntries()
        return true
      }
      return false
    } catch (error) {
      console.error('删除记录失败:', error)
      return false
    }
  }

  const fetchStatistics = async (startDate?: string, endDate?: string) => {
    try {
      const response = await Taro.request({
        url: '/api/statistics',
        method: 'GET',
        data: { startDate, endDate }
      })

      if (response.data.success) {
        statistics.value = response.data.data
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const updateTodayEntries = () => {
    const today = new Date().toISOString().split('T')[0]
    todayEntries.value = entries.value.filter(entry =>
      entry.date.startsWith(today)
    )
  }

  const clearEntries = () => {
    entries.value = []
    todayEntries.value = []
    statistics.value = null
  }

  return {
    // 状态
    entries,
    todayEntries,
    statistics,
    loading,

    // 计算属性
    latestEntry,
    todayCount,
    getEntriesByDate,
    getEntriesByEmotion,

    // 方法
    fetchEntries,
    createEntry,
    updateEntry,
    deleteEntry,
    fetchStatistics,
    updateTodayEntries,
    clearEntries
  }
})
