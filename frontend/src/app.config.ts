export default {
  pages: [
    'pages/index/index'
    // 其他页面待创建后再添加
    // 'pages/record/index',
    // 'pages/chart/index',
    // 'pages/diary/index',
    // 'pages/profile/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#8ECAE6',
    navigationBarTitleText: '今日碎语',
    navigationBarTextStyle: 'white'
  },
  // 暂时注释掉 tabBar，等图标文件准备好后再启用
  // tabBar: {
  //   color: '#888888',
  //   selectedColor: '#8ECAE6',
  //   backgroundColor: '#ffffff',
  //   borderStyle: 'black',
  //   list: [
  //     {
  //       pagePath: 'pages/index/index',
  //       text: '记录'
  //     },
  //     {
  //       pagePath: 'pages/chart/index',
  //       text: '图表'
  //     },
  //     {
  //       pagePath: 'pages/diary/index',
  //       text: '日记'
  //     }
  //   ]
  // }
}
