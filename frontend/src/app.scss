/* 自定义IconFont集成 */
@import url('./assets/fonts/fa-solid.scss');

/* ==================== 主题配置 ==================== */

/* 色板定义 - 基于需求文档 */
:root {
  /* 主色调 */
  --primary-color: #8ECAE6;
  --primary-light: #A8D8EA;
  --primary-dark: #6BB6D6;

  /* 辅助色 */
  --secondary-color: #F4F1DE;
  --secondary-light: #F8F6E8;
  --secondary-dark: #E8E4C9;

  /* 功能色 */
  --danger-color: #FA5151;
  --warning-color: #FFB84D;
  --success-color: #52C41A;
  --info-color: #1890FF;

  /* 文本色 */
  --text-primary: #2A2A2A;
  --text-secondary: #888888;
  --text-light: #CCCCCC;
  --text-white: #FFFFFF;

  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F4F1DE;
  --bg-gray: #F8F9FA;

  /* 边框色 */
  --border-color: #EAEAEA;
  --border-light: #F0F0F0;

  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.18);

  /* 圆角 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 16px;
  --radius-round: 50%;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
}

/* 字体排印规范 */
.font-title {
  font-family: 'PingFang SC', 'Roboto', 'Noto Sans', sans-serif;
  font-size: 20px;
  font-weight: bold;
  line-height: 1.4;
}

.font-heading {
  font-family: 'PingFang SC', 'Roboto', 'Noto Sans', sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.font-body {
  font-family: 'PingFang SC', 'Roboto', 'Noto Sans', sans-serif;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
}

.font-caption {
  font-family: 'PingFang SC', 'Roboto', 'Noto Sans', sans-serif;
  font-size: 12px;
  font-weight: 300;
  line-height: 1.4;
}

.font-number {
  font-family: 'DIN Alternate', 'PingFang SC', monospace;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.2;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-family: 'PingFang SC', 'Roboto', 'Noto Sans', sans-serif;
  font-size: 16px;
  line-height: 1.5;
}

/* 通用工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-light { color: var(--text-light); }
.text-white { color: var(--text-white); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-gray { background-color: var(--bg-gray); }

.border-radius-sm { border-radius: var(--radius-small); }
.border-radius-md { border-radius: var(--radius-medium); }
.border-radius-lg { border-radius: var(--radius-large); }

.shadow-light { box-shadow: var(--shadow-light); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-heavy { box-shadow: var(--shadow-heavy); }

/* 莫兰迪色系情绪色彩 */
.emotion-happy { color: #7CB083; }
.emotion-excited { color: #F4A261; }
.emotion-calm { color: #8ECAE6; }
.emotion-sad { color: #6C7B7F; }
.emotion-angry { color: #E76F51; }
.emotion-anxious { color: #B08D57; }
.emotion-tired { color: #9B8AA0; }
.emotion-confused { color: #A8DADC; }