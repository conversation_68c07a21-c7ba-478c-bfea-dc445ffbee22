<template>
  <view class="index">
    <!-- 顶部栏 -->
    <view class="header">
      <view class="header-content">
        <text class="app-title">今日碎语</text>
        <view class="user-avatar" @click="handleAvatarClick">
          <image v-if="userStore.isLoggedIn" :src="userStore.avatar" class="avatar-img" mode="aspectFill" />
          <IconFont v-else name="my2" size="24" color="#8ECAE6" />
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 登录状态检查 -->
      <view v-if="!userStore.isLoggedIn" class="login-prompt">
        <view class="prompt-card">
          <!-- 自定义表情图标 -->
          <IconFont font-class-name="emotion-icons" size="64" class-prefix="fas" name="face-smile" color="#8ECAE6" />

          <text class="prompt-title">欢迎使用今日碎语</text>
          <text class="prompt-subtitle">记录每一刻的情绪变化</text>
          <nut-button type="primary" size="large" @click="showLoginModal = true" :loading="userStore.isLoggingIn">
            开始使用
          </nut-button>
        </view>
      </view>

      <!-- 已登录状态 -->
      <view v-else class="logged-in-content">
        <!-- 焦点卡片 -->
        <view class="focus-card">
          <view class="card-header">
            <text class="card-title">今日快照</text>
            <text class="card-date">{{ currentDate }}</text>
          </view>

          <view v-if="todayEntryCount > 0" class="today-summary">
            <text class="summary-text">今天已记录 {{ todayEntryCount }} 次情绪</text>
            <view v-if="latestEntry" class="latest-entry">
              <!-- 自定义表情图标 -->
              <IconFont font-class-name="emotion-icons" class-prefix="fas" name="latestEntry.emotion.emotionIcon"
                color="latestEntry.emotion.emotionColor" />
              <text class="entry-text">{{ latestEntry.notes }}</text>
            </view>
          </view>

          <view v-else class="empty-today">
            <IconFont name="edit" size="32" color="#CCCCCC" />
            <text class="empty-text">今天还没有记录，点击下方按钮开始记录吧</text>
          </view>
        </view>

        <!-- 快速操作区域 -->
        <view class="quick-actions">
          <nut-button type="primary" size="large" block @click="goToRecord">
            <IconFont name="add" size="24" color="#fff" />
            记录情绪
          </nut-button>
        </view>
      </view>
    </view>

    <!-- 登录弹窗 -->
    <LoginModal v-model="showLoginModal" @login-success="handleLoginSuccess" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Taro from '@tarojs/taro'
import { useUserStore } from '../../store/user'
import { useEntryStore } from '../../store/entry'
import LoginModal from '../../components/LoginModal.vue'
import { IconFont } from '@nutui/icons-vue-taro'

// Store
const userStore = useUserStore()
const entryStore = useEntryStore()

// 响应式数据
const showLoginModal = ref(false)

// 计算属性
const currentDate = computed(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
})

const todayEntryCount = computed(() => {
  return entryStore.todayCount
})

const latestEntry = computed(() => {
  return entryStore.latestEntry
})

// 方法
const handleAvatarClick = () => {
  if (userStore.isLoggedIn) {
    // 跳转到个人中心
    Taro.navigateTo({
      url: '/pages/profile/index'
    })
  } else {
    showLoginModal.value = true
  }
}

const handleLoginSuccess = () => {
  console.log('登录成功，刷新页面数据')
  // 登录成功后可以加载用户数据
  loadUserData()
}

const goToRecord = () => {
  Taro.navigateTo({
    url: '/pages/record/index'
  })
}

const loadUserData = async () => {
  if (userStore.isLoggedIn) {
    try {
      // 加载今日记录
      await entryStore.fetchEntries(1, 10)
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  }
}

// 生命周期
onMounted(async () => {
  console.log('首页组件已挂载')

  // 初始化用户状态
  await userStore.initializeFromStorage()

  // 如果已登录，加载用户数据
  if (userStore.isLoggedIn) {
    await loadUserData()
  }
})
</script>

<style lang="scss" scoped>
.index {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--bg-secondary) 30%);
  position: relative;
}

.header {
  background: transparent;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xl);
  position: relative;
  z-index: 10;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: env(safe-area-inset-top, 0);

    .app-title {
      font-size: 22px;
      font-weight: 600;
      color: var(--text-white);
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .user-avatar {
      width: 44px;
      height: 44px;
      border-radius: var(--radius-round);
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 0.25);
      }

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: var(--radius-round);
      }
    }
  }
}

.main-content {
  padding: 0 var(--spacing-md) var(--spacing-lg);
  margin-top: -var(--spacing-lg);
  position: relative;
  z-index: 5;
}

.login-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 65vh;
  padding: var(--spacing-lg) 0;

  .prompt-card {
    background: var(--bg-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-xxl) var(--spacing-xl);
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    max-width: 320px;
    width: 100%;
    margin: 0 var(--spacing-md);
    position: relative;
    overflow: hidden;

    // 添加微妙的背景装饰
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .prompt-title {
      display: block;
      font-size: 26px;
      font-weight: 600;
      color: var(--text-primary);
      margin: var(--spacing-lg) 0 var(--spacing-sm);
      line-height: 1.3;
    }

    .prompt-subtitle {
      display: block;
      font-size: 15px;
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xxl);
      line-height: 1.5;
      opacity: 0.8;
    }

    // 按钮容器样式优化
    :deep(.nut-button) {
      width: 100%;
      height: 52px;
      font-size: 16px;
      font-weight: 500;
      border-radius: var(--radius-medium);
      box-shadow: 0 4px 16px rgba(142, 202, 230, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(142, 202, 230, 0.4);
      }
    }
  }
}

.logged-in-content {
  .focus-card {
    background: var(--bg-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(142, 202, 230, 0.1);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .card-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--text-primary);
      }

      .card-date {
        font-size: 13px;
        color: var(--text-secondary);
        background: var(--bg-gray);
        padding: 4px 8px;
        border-radius: var(--radius-small);
      }
    }

    .today-summary {
      .summary-text {
        display: block;
        font-size: 15px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
        font-weight: 500;
      }

      .latest-entry {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: linear-gradient(135deg, var(--bg-gray) 0%, rgba(142, 202, 230, 0.05) 100%);
        border-radius: var(--radius-medium);
        border-left: 3px solid var(--primary-color);

        .entry-text {
          font-size: 15px;
          color: var(--text-primary);
          flex: 1;
          line-height: 1.5;
        }
      }
    }

    .empty-today {
      text-align: center;
      padding: var(--spacing-xxl) var(--spacing-md);

      .empty-text {
        display: block;
        font-size: 15px;
        color: var(--text-light);
        margin-top: var(--spacing-lg);
        line-height: 1.5;
      }
    }
  }

  .quick-actions {
    margin-top: var(--spacing-xl);
    padding: 0 var(--spacing-sm);

    :deep(.nut-button) {
      height: 56px;
      font-size: 16px;
      font-weight: 500;
      border-radius: var(--radius-medium);
      box-shadow: 0 4px 16px rgba(142, 202, 230, 0.25);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(142, 202, 230, 0.35);
      }

      // 图标和文字的间距
      .nut-icon {
        margin-right: var(--spacing-sm);
      }
    }
  }
}

// NutUI 组件样式覆盖 - 针对小程序环境的特殊优化
:deep(.nut-button--primary) {
  // 确保主题色正确应用
  background: var(--nut-button-primary-background-color, linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%)) !important;
  border-color: var(--nut-button-primary-border-color, var(--primary-color)) !important;
  color: var(--nut-button-primary-color, var(--text-white)) !important;

  // 小程序环境下的触摸反馈
  &:active {
    background: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    opacity: 0.9;
  }
}

// 修复小程序环境下的样式问题
:deep(.nut-popup) {
  .nut-popup__content {
    border-radius: var(--nut-popup-border-radius, var(--radius-large)) !important;
    overflow: hidden;
  }
}

// 小程序安全区域适配
@supports (padding: env(safe-area-inset-bottom)) {
  .index {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .main-content {
    padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom));
  }
}

// 小程序特定的样式修复
@media screen and (max-width: 750px) {
  .login-prompt .prompt-card {
    margin: 0 var(--spacing-lg);
    padding: var(--spacing-xl) var(--spacing-lg);

    .prompt-title {
      font-size: 24px;
    }
  }

  .logged-in-content .focus-card {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
}
</style>