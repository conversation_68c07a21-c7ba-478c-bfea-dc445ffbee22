<template>
  <view class="index">
    <!-- 顶部栏 -->
    <view class="header">
      <view class="header-content">
        <text class="app-title">今日碎语</text>
        <view class="user-avatar" @click="handleAvatarClick">
          <image v-if="userStore.isLoggedIn" :src="userStore.avatar" class="avatar-img" mode="aspectFill" />
          <IconFont v-else name="my2" size="24" color="#8ECAE6" />
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 登录状态检查 -->
      <view v-if="!userStore.isLoggedIn" class="login-prompt">
        <view class="prompt-card">
          <!-- 自定义表情图标 -->
          <IconFont font-class-name="emotion-icons" size="64" class-prefix="fas" name="face-smile" color="#8ECAE6" />

          <text class="prompt-title">欢迎使用今日碎语</text>
          <text class="prompt-subtitle">记录每一刻的情绪变化</text>
          <nut-button type="primary" size="large" @click="showLoginModal = true" :loading="userStore.isLoggingIn">
            开始使用
          </nut-button>
        </view>
      </view>

      <!-- 已登录状态 -->
      <view v-else class="logged-in-content">
        <!-- 焦点卡片 -->
        <view class="focus-card">
          <view class="card-header">
            <text class="card-title">今日快照</text>
            <text class="card-date">{{ currentDate }}</text>
          </view>

          <view v-if="todayEntryCount > 0" class="today-summary">
            <text class="summary-text">今天已记录 {{ todayEntryCount }} 次情绪</text>
            <view v-if="latestEntry" class="latest-entry">
              <!-- 自定义表情图标 -->
              <IconFont font-class-name="emotion-icons" class-prefix="fas" name="latestEntry.emotion.emotionIcon"
                color="latestEntry.emotion.emotionColor" />
              <text class="entry-text">{{ latestEntry.notes }}</text>
            </view>
          </view>

          <view v-else class="empty-today">
            <IconFont name="edit" size="32" color="#CCCCCC" />
            <text class="empty-text">今天还没有记录，点击下方按钮开始记录吧</text>
          </view>
        </view>

        <!-- 快速操作区域 -->
        <view class="quick-actions">
          <nut-button type="primary" size="large" block @click="goToRecord">
            <IconFont name="add" size="24" color="#fff" />
            记录情绪
          </nut-button>
        </view>
      </view>
    </view>

    <!-- 登录弹窗 -->
    <LoginModal v-model="showLoginModal" @login-success="handleLoginSuccess" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Taro from '@tarojs/taro'
import { useUserStore } from '../../store/user'
import { useEntryStore } from '../../store/entry'
import LoginModal from '../../components/LoginModal.vue'
import { IconFont } from '@nutui/icons-vue-taro'

// Store
const userStore = useUserStore()
const entryStore = useEntryStore()

// 响应式数据
const showLoginModal = ref(false)

// 计算属性
const currentDate = computed(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
})

const todayEntryCount = computed(() => {
  return entryStore.todayCount
})

const latestEntry = computed(() => {
  return entryStore.latestEntry
})

// 方法
const handleAvatarClick = () => {
  if (userStore.isLoggedIn) {
    // 跳转到个人中心
    Taro.navigateTo({
      url: '/pages/profile/index'
    })
  } else {
    showLoginModal.value = true
  }
}

const handleLoginSuccess = () => {
  console.log('登录成功，刷新页面数据')
  // 登录成功后可以加载用户数据
  loadUserData()
}

const goToRecord = () => {
  Taro.navigateTo({
    url: '/pages/record/index'
  })
}

const loadUserData = async () => {
  if (userStore.isLoggedIn) {
    try {
      // 加载今日记录
      await entryStore.fetchEntries(1, 10)
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  }
}

// 生命周期
onMounted(async () => {
  console.log('首页组件已挂载')

  // 初始化用户状态
  await userStore.initializeFromStorage()

  // 如果已登录，加载用户数据
  if (userStore.isLoggedIn) {
    await loadUserData()
  }
})
</script>

<style lang="scss" scoped>
.index {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.header {
  background: var(--primary-color);
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-lg);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .app-title {
      font-size: 20px;
      font-weight: bold;
      color: var(--text-white);
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-round);
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: var(--radius-round);
      }
    }
  }
}

.main-content {
  padding: var(--spacing-lg) var(--spacing-md);
}

.login-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;

  .prompt-card {
    background: var(--bg-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-xxl) var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-medium);
    max-width: 300px;
    width: 100%;

    .prompt-title {
      display: block;
      font-size: 24px;
      font-weight: bold;
      color: var(--text-primary);
      margin: var(--spacing-lg) 0 var(--spacing-sm);
    }

    .prompt-subtitle {
      display: block;
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xl);
      line-height: 1.4;
    }
  }
}

.logged-in-content {
  .focus-card {
    background: var(--bg-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
      }

      .card-date {
        font-size: 12px;
        color: var(--text-secondary);
      }
    }

    .today-summary {
      .summary-text {
        display: block;
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
      }

      .latest-entry {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        background: var(--bg-gray);
        border-radius: var(--radius-medium);

        .entry-text {
          font-size: 14px;
          color: var(--text-primary);
          flex: 1;
          line-height: 1.4;
        }
      }
    }

    .empty-today {
      text-align: center;
      padding: var(--spacing-xl) var(--spacing-md);

      .empty-text {
        display: block;
        font-size: 14px;
        color: var(--text-light);
        margin-top: var(--spacing-md);
        line-height: 1.4;
      }
    }
  }

  .quick-actions {
    margin-top: var(--spacing-lg);
  }
}

// NutUI 组件样式覆盖 - 现在主要通过主题变量控制，这里只做必要的补充
:deep(.nut-button--primary) {
  // 主题变量已经处理了基本样式，这里只做特殊情况的覆盖
  &:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
  }
}
</style>