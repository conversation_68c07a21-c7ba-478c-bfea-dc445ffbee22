@font-face {
  font-family: 'emotion-icons';
  src: url('fa-solid-900.ttf') format('truetype');
  /* 确保这个路径相对于你的 SCSS 文件是正确的 */
  font-weight: 900;
  /* 'fa-solid-900.ttf' 通常是 Solid 900 weight */
  font-style: normal;
}

/* 通用类，用于应用字体 (如果需要的话，或者直接在图标类上指定 font-family) */
/* 如果你的每个.fas-xxx::before 都会在元素上同时有 .fontawesome 类，这个可以省略 */
/* 否则，你需要在每个 .fas-xxx 类中也加上   font-family: 'emotion-icons'; */
.emotion-icons {
  font-family: 'emotion-icons';
  font-weight: 900;
  /* 确保使用正确的字重 */
  -webkit-font-smoothing: antialiased;
  /* 改善渲染 */
  -moz-osx-font-smoothing: grayscale;
  /* 改善渲染 */
  display: inline-block;
  /* 确保图标正确显示 */
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* ------------------------- */
/* 表情类 (Emotions) 图标定义  */
/* ------------------------- */

/* Positive Emotions - 积极情绪 */
.fas-face-smile::before {
  /* 微笑 😊 */
  font-family: 'emotion-icons';
  /* 确保每个图标类都应用字体 */
  font-weight: 900;
  content: '\f118';
}

.fas-face-laugh-beam::before {
  /* 开心大笑 😄 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f59a';
}

.fas-face-grin-hearts::before {
  /* 爱心眼 😍 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f584';
}

.fas-face-grin-stars::before {
  /* 星星眼 🤩 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f587';
}

.fas-face-grin-beam::before {
  /* 咧嘴笑 😁 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f582';
}

.fas-face-kiss-wink-heart::before {
  /* 飞吻 😘 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f598';
}


/* Neutral Emotions - 中性情绪 */
.fas-face-meh::before {
  /* 无语/一般 😐 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f11a';
}

.fas-face-rolling-eyes::before {
  /* 翻白眼 🙄 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f5a5';
}

.fas-face-flushed::before {
  /* 脸红/惊讶 😳 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f579';
}


/* Negative Emotions - 消极情绪 */
.fas-face-frown::before {
  /* 不悦/皱眉 🙁 / ☹️ */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f119';
}

.fas-face-sad-tear::before {
  /* 伤心流泪 😢 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f5b4';
}

.fas-face-sad-cry::before {
  /* 大哭 😭 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f5b3';
}

.fas-face-angry::before {
  /* 生气 😠 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f556';
}

.fas-face-tired::before {
  /* 疲惫 😩 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f5c8';
}

.fas-face-dizzy::before {
  /* 晕眩/困惑 😵 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f567';
}

.fas-face-grimace::before {
  /* 痛苦/鬼脸 😬 */
  font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f57f';
}

/* ------------------------- */
/* 你可以继续添加更多图标...   */
/* 例如：
.fas-face-surprise::before { // 惊讶 😮
    font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f5c2';
}
.fas-face-thinking::before { // 思考 🤔
    font-family: 'emotion-icons';
  font-weight: 900;
  content: '\f9ae'; // 这个可能是 FA Pro 的，请确认
}
*/