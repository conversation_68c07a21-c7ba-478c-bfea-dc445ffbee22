import Taro from '@tarojs/taro'

// API 基础配置
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:8080/api' 
  : 'https://your-production-api.com/api'

// 微信登录请求接口
export interface WeChatLoginRequest {
  code: string
  nickname?: string
  avatarUrl?: string
}

// 微信登录响应接口
export interface WeChatLoginResponse {
  code: number
  message: string
  data: {
    token: string
    userId: string
    nickname: string
    user?: {
      userId: string
      openId: string
      unionId?: string
      username: string
      nickname: string
      avatarUrl?: string
      lastLoginTime: string
      createdAt: string
    }
  }
}

// 用户信息接口
export interface UserProfile {
  code: number
  message: string
  data: {
    userId: string
    openId: string
    unionId?: string
    username: string
    nickname: string
    avatarUrl?: string
    lastLoginTime: string
    createdAt: string
  }
}

/**
 * 微信授权登录服务类
 */
export class AuthService {
  
  /**
   * 获取微信登录凭证
   */
  static async getWeChatCode(): Promise<string> {
    try {
      const result = await Taro.login()
      if (result.code) {
        console.log('微信登录凭证获取成功:', result.code)
        return result.code
      } else {
        throw new Error('获取微信登录凭证失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      throw new Error('微信登录失败，请重试')
    }
  }

  /**
   * 获取用户信息（需要用户授权）
   */
  static async getUserProfile(): Promise<{ nickname: string; avatarUrl: string }> {
    try {
      // 检查是否支持 getUserProfile
      if (Taro.getUserProfile) {
        const result = await Taro.getUserProfile({
          desc: '用于完善用户资料'
        })
        
        return {
          nickname: result.userInfo.nickName,
          avatarUrl: result.userInfo.avatarUrl
        }
      } else {
        // 降级处理：使用 getUserInfo（可能需要用户授权）
        const result = await Taro.getUserInfo()
        return {
          nickname: result.userInfo.nickName,
          avatarUrl: result.userInfo.avatarUrl
        }
      }
    } catch (error) {
      console.warn('获取用户信息失败，使用默认信息:', error)
      // 返回默认信息，不阻断登录流程
      return {
        nickname: '微信用户',
        avatarUrl: ''
      }
    }
  }

  /**
   * 调用后端微信登录接口
   */
  static async weChatLogin(loginData: WeChatLoginRequest): Promise<WeChatLoginResponse> {
    try {
      const response = await Taro.request({
        url: `${API_BASE_URL}/auth/wechat-login`,
        method: 'POST',
        data: loginData,
        header: {
          'Content-Type': 'application/json'
        }
      })

      console.log('后端登录响应:', response.data)

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data as WeChatLoginResponse
      } else {
        throw new Error(response.data.message || '登录失败')
      }
    } catch (error) {
      console.error('后端登录请求失败:', error)
      throw new Error('网络请求失败，请检查网络连接')
    }
  }

  /**
   * 获取用户详细信息
   */
  static async getUserInfo(token: string): Promise<UserProfile> {
    try {
      const response = await Taro.request({
        url: `${API_BASE_URL}/user/profile`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data as UserProfile
      } else {
        throw new Error(response.data.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw new Error('获取用户信息失败')
    }
  }

  /**
   * 检查token有效性
   */
  static async validateToken(token: string): Promise<boolean> {
    try {
      await this.getUserInfo(token)
      return true
    } catch (error) {
      console.warn('Token验证失败:', error)
      return false
    }
  }

  /**
   * 完整的微信登录流程
   */
  static async performWeChatLogin(withUserInfo: boolean = true): Promise<WeChatLoginResponse> {
    try {
      // 1. 获取微信登录凭证
      const code = await this.getWeChatCode()
      
      // 2. 准备登录数据
      const loginData: WeChatLoginRequest = { code }
      
      // 3. 如果需要用户信息，尝试获取
      if (withUserInfo) {
        try {
          const userInfo = await this.getUserProfile()
          loginData.nickname = userInfo.nickname
          loginData.avatarUrl = userInfo.avatarUrl
        } catch (error) {
          console.warn('获取用户信息失败，继续登录流程:', error)
        }
      }
      
      // 4. 调用后端登录接口
      const loginResponse = await this.weChatLogin(loginData)
      
      return loginResponse
    } catch (error) {
      console.error('微信登录流程失败:', error)
      throw error
    }
  }
}

export default AuthService
