# **今日碎语小程序需求文档**

---

**版本历史**

| 版本 | 日期         | 主要变更                                 | 修订者 |
| ---- | ------------ | ---------------------------------------- | ------ |
| 1.0  | [初始日期]   | 初始草案                                 |        |
---

## **1. 产品愿景与定位**

**核心功能：**

1.  快速情绪记录（情绪图标 + 原因分类 + 文本笔记）。
2.  多维度数据可视化与分析（情绪与原因；日/周/月视图）。
3.  一键微信授权登录（唯一登录方式）。

**设计原则：**

-   **舒缓治愈的视觉语言：** 莫兰迪色系 + 圆角，"呼吸感"留白设计。
-   **零学习成本：** 语义清晰的图标，核心操作用户流程不超过3次点击。
-   **沉浸式体验：** 全屏、无干扰记录模式；支持手势导航（如滑动返回）。

---

## **2. 页面结构与UI细节**

### **2.1. 记录页 (默认视图 / “首页”)**

**布局：**

```
[顶部栏]
  - 标题: "今日碎语" (字体: 系统默认, 大小: 22px, 字重: Medium/Semibold)
  - 右侧: 用户头像按钮 (点击跳转至个人中心)

[主要操作区]
  - 焦点卡片: "今日最新碎语快照"
    - 若今日已有记录:
      - 大号情绪图标 (来自最新记录, 通过自定义IconFont渲染)
      - 情绪标签 (来自最新记录)
      - 情绪强度 (1-5点: ●○○○○ 至 ●●●●●)
      - 最新记录时间戳 (例如："下午 3:15")
      - [可选] 笔记预览 (例如：前20个字符)
    - 若今日无记录:
      - 温馨问候语 (例如："今天感觉怎么样？")
      - [可选] 柔和背景插画/图案
  - 快速记录按钮 (浮动操作按钮, 直径72px, 点击跳转至情绪记录页)

[底部导航栏]
  - 记录 (激活状态图标)
  - 图表 (条形图图标)
  - 日记 (笔记本图标)
```

**交互：**

-   点击焦点卡片会打开一个“情绪趋势预览”弹窗。若今日无记录，点击则跳转至情绪记录页。
-   下拉刷新同步数据（显示波纹加载动画）。

---

### **2.2. 情绪记录页**

**布局：**

```
[头部]
  - 返回按钮 (左上角)
  - 时间戳 (居中, 例如："下午 2:32 • 今天")

[情绪选择器]
  - 3x3 网格 (间距: 24px)
  - 每个情绪单元格:
    • 中心图标 (自定义IconFont, class来自 `Emotions.emotion_icon`, 默认文本颜色, 选中时外圈高亮/脉冲效果，颜色为 `Emotions.emotion_color`; 48x48px视觉容器)
    • 底部标签 (垂直, `Emotions.emotion_label`)

[原因分类区]
  - 水平滚动标签栏 (数据源: DISTINCT `Causes.cause_category`)
    例如："工作" | "社交" | "健康" | "生活" | "其他"
  - 子选项瀑布流 (每行3列, 圆角胶囊按钮; 数据源: `Causes.cause_label`, 根据选中的 `cause_category` 过滤)

[笔记输入区]
  - 自动调整大小的文本输入框 (最小高度: 120px)
  - 实时字数统计 (右下角, "0/500")
  - 底部工具栏: Emoji选择器图标 | 语音输入图标 (语音转文字到输入框)

[提交按钮区]
  - 固定底部栏 (带阴影分割线)
  - "保存记录" 按钮 (禁用时透明度50%)
```

**交互：**

-   若必填项（情绪、原因）未选择，提交按钮禁用。
-   输入框背景可根据选中的 `Emotions.emotion_color` 发生细微变化/渐变。

---

### **2.3. 图表页 (Dashboard Page) - Simplified (No Complex Charting Libraries)**

**布局：**

```
[时间筛选栏]
  - 日期选择器 (左对齐, 带日历图标)
  - 视图切换: 日 | 周 | 月 (胶囊按钮组 - NutUI)

[图表类型切换]
  - 标签页/按钮组: [ 情绪分析 | 原因分析 ] (NutUI Tabs 组件)

[内容区 - 根据图表类型切换动态显示]

  [[若选中 "情绪分析"]]
    **[情绪时间轴概览 (Emotion Timeline Overview) - 主要用于 "日" 视图]**
      - 视觉表现:
        - 水平简化的时间轴 (例如：早上6:00 → 晚上10:00, 或根据当日记录动态生成刻度)。
        - 在时间轴上，用情绪图标 (`Emotions.emotion_icon`) 或彩色圆点 (颜色来自 `Emotions.emotion_color`) 标记每个情绪记录的发生时间点。
        - 情绪强度 (`Entries.intensity`) 通过圆点大小 (例如：3种固定大小 S/M/L 对应 1-2 / 3 / 4-5 强度) 或图标旁小数字表示。
      - 交互:
        - 点击标记点弹出一个精简卡片，显示：情绪标签、具体时间、笔记预览。
      - *注意: 此视图在“周”或“月”视图下可能过于拥挤，可隐藏或优先显示下面的“情绪统计列表”。*

    **[情绪统计列表 (Emotion Statistics List)]**
      - 视觉表现:
        - 标题: "[选定时间范围] 内的情绪分布"
        - 列表项 (每种被记录过的情绪一行):
          - 情绪图标 (`Emotions.emotion_icon` & `Emotions.emotion_color`)
          - 情绪标签 (`Emotions.emotion_label`)
          - 频次条/计数:
            - 简单的水平条 (用 `<div>` 和 `background-color` 实现，长度与频次/百分比成正比)。
            - 或直接显示计数 (例如："出现 5 次") 和百分比 (例如："占 25%")。
          - [可选] 平均强度。
      - 交互:
        - 列表按频次降序排列。

  [[若选中 "原因分析"]]
    **[原因图表子切换]** (NutUI Tabs 组件)
      - 标签页/按钮组: [ 按类别 | 按具体原因 ]

    **[原因统计列表 (Reason Statistics List)]**
      - 视觉表现:
        - 标题: "[选定时间范围] 内的原因分布 ([按类别/按具体原因])"
        - 列表项 (每个原因类别/具体原因一行):
          - 原因标签 (`Causes.cause_category` 或 `Causes.cause_label`)
          - 频次条/计数:
            - 类似情绪统计列表中的水平条。
            - 或直接显示计数和百分比。
          - [可选] 关联最常见情绪。
      - 交互:
        - 列表按频次降序排列。
        - 若为 "按类别"，点击类别可考虑展开显示该类别下的具体原因及其频次。

[洞察总结]
  - 文本卡片 (浅色背景, 内容根据当前分析视图动态生成):
    - "此[时间范围]内最频繁的情绪: [情绪标签] ([计数]次, 占[百分比]%)"
    - "常见原因类别: [原因类别] ([计数]次, 占[百分比]%)" (若在原因类别视图)
    - "关键具体原因: [原因标签] ([计数]次, 占[百分比]%)" (若在具体原因视图)
    - "情绪记录总数: [总数] 条"
```

**交互：**

-   当“图表类型切换”或“原因图表子切换”选项改变时，内容区动态更新。
-   “情绪时间轴概览”在日视图下若内容过多，可支持水平滚动。
-   点击时间轴上的标记点或统计列表中的项目，可以显示原始记录摘要的弹窗或进行简单高亮。
-   当无数据时，所有列表和时间轴显示统一的空状态（插画 + 引导文字，例如：“此时间段内暂无数据，去记录一下吧！”）。

---

### **2.4. 日记页**

**布局：**

```
[筛选栏]
  - 情绪筛选 (下拉菜单, 带颜色指示, 基于 `Emotions` 表)
  - 日期筛选 (YYYY-MM-DD 格式)

[日记条目列表]
  - 卡片式瀑布流 (每行1列)
  - 卡片内容:
    • 头部: 情绪图标 (自定义IconFont, class来自 `entry.emotion.iconClass`, 颜色来自 `entry.emotion.color`) + 情绪强度 (点状表示)
    • 内容预览: "今天的会议压力好大..." (`Entries.notes` 预览, 最多3行)
    • 原因标签 (灰色圆角矩形, `Causes.cause_label`)
    • 时间戳 (例如："下午 2:32", 来自 `Entries.date`)

[空状态]
  - 插画 + 文字: "还没有任何记录"
  - 主要操作按钮: "开始第一条记录" (跳转至情绪记录页)
```

**交互：**

-   点击卡片打开详情弹窗（完整笔记、情绪、原因、时间等）。
-   卡片左滑显示“编辑”和“删除”按钮。
-   删除操作需要确认对话框（警告红色调，例如：“确定要删除这条记录吗？此操作无法撤销。”）。

---

### **2.5. 个人中心页**

**布局：**

```
[用户信息区]
  - 头像 (圆形, 直径80px, 带边框, `Users.avatar_url`)
  - 昵称 (头像下方居中, `Users.nickname`)
  - "上次登录: [Users.last_login_time]" (小号辅助文字, 格式 YYYY-MM-DD HH:mm)

[功能入口] (列表样式, 每行一项, 左侧图标, 右侧文字, 箭头指示)
  - 自定义情绪库 (Font Awesome `fas fa-palette` 或类似图标)
  - 帮助中心 (Font Awesome `far fa-question-circle` 或类似图标)
  - [可选] 关于我们 (Font Awesome `fas fa-info-circle` 或类似图标)
  - [可选] 安全设置 (Font Awesome `fas fa-lock` 或类似图标)

[底部区域]
  - 版本号 (页面底部居中或右下角浮动 "版本 X.Y.Z")
  - 退出登录按钮 (红色文字按钮, 底部居中, 明显分隔)
```

**交互：**

-   用户信息区背景可巧妙匹配头像主色调或使用统一浅色背景。
-   点击功能入口跳转至相应页面或触发操作，并有点击反馈。
-   点击版本号可触发彩蛋动画（例如：细微的粒子效果）。

---

## **3. 视觉规范**

### **3.1. 色板**

| 类型       | 主要十六进制值 | 使用场景                                   |
| ---------- | -------------- | ------------------------------------------ |
| 主色调     | `#8ECAE6`      | 按钮、主要视觉元素                         |
| 情绪色相   | 多种           | 情绪图标背景/强调色 (来自 `Emotions.emotion_color`) |
| 辅助色     | `#F4F1DE`      | 背景、卡片底色                             |
| 警告/危险色 | `#FA5151`      | 删除操作、警告提示                         |
| 文本色     | `#2A2A2A`      | 主要文本内容                               |
| 浅灰文本色 | `#888888`      | 辅助文本、次要信息                         |
| 分割线     | `#EAEAEA`      | 内容分隔                                   |

### **3.2. 字体排印**

| 场景         | 字体栈 (iOS优先, Android备用)      | 大小 | 字重          |
| ------------ | ---------------------------------- | ---- | ------------- |
| 页面标题     | PingFang SC, Roboto/Noto Sans      | 20px | Bold          |
| 正文/列表文本 | PingFang SC, Roboto/Noto Sans      | 16px | Regular       |
| 辅助文本     | PingFang SC, Roboto/Noto Sans      | 12px | Light         |
| 数据标签     | DIN Alternate (或系统数字优化字体) | 14px | Bold          |
| 首页标题     | 系统默认                           | 22px | Medium/Semibold |

*注意：优先使用系统原生字体以保证跨平台一致性和性能。*

### **3.3. 图标系统**

-   **情绪图标**：Fluent Design 风格，单色线性图标，通过**自定义IconFont**实现。`Emotions.emotion_icon` 在数据库中存储对应的CSS类名。图标颜色通过 `Emotions.emotion_color` 动态设置。视觉尺寸约24px。
-   **功能图标**：使用**Font Awesome (免费版)，通过SVG与JavaScript结合实现** (例如，使用 @fortawesome/vue-fontawesome)。这样可以实现更好的摇树优化 (tree-shaking) 和灵活性。具体图标将按需导入 (例如：faCog, faQuestionCircle)。
-   **加载动画**：三瓣呼吸圆环 (主色调 #8ECAE6，带颜色渐隐效果)。

---

## **4. 导航流程**

```
微信授权登录 → 记录页 (默认视图)
  ├── 点击浮动操作按钮 → 情绪记录页
  ├── 点击底部导航 → 记录页 | 图表页 | 日记页
  └── 点击头像 → 个人中心页

记录页
  ├── 点击焦点卡片 (有记录时) → 情绪趋势预览弹窗
  └── 点击焦点卡片 (无记录时) → 情绪记录页

情绪记录页
  ├── 保存成功 → 返回记录页 (焦点卡片刷新)
  └── 点击原因分类 (父级) -> 更新子选项瀑布流

图表页
  ├── 筛选变更 / 图表类型切换 → 内容区域局部刷新
  └── 点击时间轴标记点或统计列表项 → 显示原始记录摘要的弹窗

日记页
  └── 点击列表项 → 详情弹窗
  └── 左滑操作 → 编辑/删除选项

个人中心页
  └── 点击功能入口 → 相应页面/操作
```

---

## **5. 数据模型**

### **5.1. 用户表 (Users Table)**

| 字段                                                         | 类型                  | 描述                                                                 |
| ------------------------------------------------------------ | --------------------- | -------------------------------------------------------------------- |
| user_id                                                      | VARCHAR(128)          | 主键, 内部用户唯一标识符 (例如：后端生成的UUID)。                          |
| **open_id**                                                  | **VARCHAR(128)**      | **此小程序唯一的微信OpenID。** (索引)                                      |
| **union_id**                                                 | **VARCHAR(128) NULL** | **微信UnionID (若适用, 用于跨多个微信应用/平台关联)。** (索引, 可为空)        |
| **username**                                                 | **VARCHAR(50)**       | **用户选择的显示名称或初始微信昵称 (用户可编辑)。**                             |
| avatar_url                                                   | VARCHAR(255) NULL     | 用户选择的头像URL或初始微信头像URL (可编辑, 可为空)。                         |
| session_key                                                  | VARCHAR(255)          | 微信Session Key (后端加密存储, 不暴露给前端)。                              |
| last_login_time                                              | TIMESTAMP             | 上次登录时间戳。                                                         |
| created_at                                                   | TIMESTAMP             | 账户创建时间戳 (默认为 CURRENT_TIMESTAMP)。                               |
| updated_at                                                   | TIMESTAMP             | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)。         |
| *一个特定的 user_id (例如："0" 或 "system_user") 将代表系统用户。* |                       |                                                                      |
| *注意: open_id 是此特定应用与微信身份的主要关联。user_id 是内部ID。* |                       |                                                                      |

### **5.2. 日记条目表 (Entries Table - 情绪日记条目)**

| 字段       | 类型                  | 描述                                                                 |
| ---------- | --------------------- | -------------------------------------------------------------------- |
| entry_id   | BIGINT AUTO_INCREMENT | 主键                                                                   |
| user_id    | VARCHAR(128)          | 外键, 引用 Users 表                                                    |
| date       | TIMESTAMP             | 记录时间戳 (精确到秒, 前端提供, 后端校验)                                  |
| notes      | TEXT                  | 日记内容 (静态加密/传输中加密)                                             |
| cause_id   | INT                   | 外键, 引用 Causes 表 (单选, 必填)                                       |
| emotion_id | INT                   | 外键, 引用 Emotions 表 (单选, 必填)                                      |
| intensity  | TINYINT               | 情绪强度 (1-5, 必填)                                                   |
| created_at | TIMESTAMP             | 记录创建时间戳 (默认为 CURRENT_TIMESTAMP)                                  |
| updated_at | TIMESTAMP             | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)        |

### **5.3. 情绪表 (Emotions Table - 情绪定义)**

| 字段          | 类型               | 描述                                                                 |
| ------------- | ------------------ | -------------------------------------------------------------------- |
| emotion_id    | INT AUTO_INCREMENT | 主键                                                                   |
| emotion_label | VARCHAR(50)        | 情绪标签 (例如："焦虑", 唯一)                                              |
| emotion_icon  | VARCHAR(50)        | IconFont的CSS类名 (例如："icon-fluent-anxious")                          |
| emotion_color | VARCHAR(20)        | HEX颜色代码 (例如："#FF6B6B") 用于IconFont颜色及相关UI元素                 |
| is_system     | BOOLEAN            | 若为系统定义则为True, 用户定义则为False                                   |
| user_id       | VARCHAR(128)       | 外键, 引用 Users 表 (系统情绪关联到系统用户ID, 自定义情绪关联到实际用户ID) |
| sort_order    | INT                | [可选] 网格/列表的显示顺序                                                |
| created_at    | TIMESTAMP          | 创建时间戳 (默认为 CURRENT_TIMESTAMP)                                      |
| updated_at    | TIMESTAMP          | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)        |

### **5.4. 原因表 (Causes Table - 原因定义)**

| 字段           | 类型               | 描述                                                                 |
| -------------- | ------------------ | -------------------------------------------------------------------- |
| cause_id       | INT AUTO_INCREMENT | 主键                                                                   |
| cause_label    | VARCHAR(50)        | 原因标签 (例如："工作压力", 在一个分类内或用户级别唯一)                       |
| cause_category | VARCHAR(50)        | 类别标签 (例如："工作", 系统定义或用户可自定义)                              |
| is_system      | BOOLEAN            | 若为系统定义则为True, 用户定义则为False                                   |
| user_id        | VARCHAR(128)       | 外键, 引用 Users 表 (逻辑同 `Emotions.user_id`)                        |
| sort_order     | INT                | [可选] 显示顺序                                                         |
| created_at     | TIMESTAMP          | 创建时间戳 (默认为 CURRENT_TIMESTAMP)                                      |
| updated_at     | TIMESTAMP          | 最后更新时间戳 (默认为 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)        |

---

## **6. 技术栈与实现说明**

-   **前端：** Taro4 + Vue3 + TypeScript
-   **情绪图标实现：** 自定义IconFont。Fluent Design风格单色线性图标。`Emotions.emotion_icon` 存储CSS类名。颜色通过 `Emotions.emotion_color` 动态设置。
-   **功能图标实现：** **Font Awesome (免费版) 通过SVG与JavaScript结合** (使用 @fortawesome/fontawesome-svg-core, 特定风格的图标包如 @fortawesome/free-solid-svg-icons, 以及 @fortawesome/vue-fontawesome)。图标将显式导入库中以进行摇树优化 (tree-shaking)。
-   **UI组件：** NutUI 4.x for Vue3 (用于布局、表单、导航、Tabs、按钮组等)。
-   **状态管理：** Pinia。
-   **数据存储与同步：** 后端API + **MySQL** 关系型数据库。
-   **图表页数据可视化：** 通过自定义Vue组件结合HTML/CSS实现简化的数据概览，如时间轴标记、统计列表和频次条，不依赖复杂图表库。
-   **系统用户：** 一个专用的 `user_id` (例如："0" 或 "system_user") 将用于标识预定义情绪和原因的系统级记录。
-   **加密：** 敏感用户数据（例如：session_key）在后端加密。日记笔记应在传输和静态存储时加密。

---

## **7. 非功能性需求 (初步)**

-   **性能：** 页面加载时间应在3秒以内。UI交互应流畅且响应迅速。
-   **安全：** 用户数据（尤其是日记条目和微信授权详情）必须得到安全处理，并防止未经授权的访问或泄露。
-   **兼容性：** 兼容Taro支持的主流iOS和Android系统版本。
-   **易用性：** 遵守微信小程序设计指南，确保易于使用。
