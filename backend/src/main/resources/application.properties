# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Database Configuration
spring.datasource.url=************************************************************************************************
spring.datasource.username=root
spring.datasource.password=1q2w3e4R
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379

# JWT Configuration
app.jwt.secret=a0cfcd33eab5266d356a14c39ac780a62734b599109116db04befcda0389bda3c2e088b277b0c8f2994bc3051e36803f7c031a3d048cb0d2ba2628dd92fa64aa9f554fb2f4a9f5706020cb0723d9f3ef3faa62ea938ec881eac1c879cdba251cf36d166e0c052986760bad6cddec7fe66dfbdcad7b1e7a27808f09412be3c27a
app.jwt.expiration-ms=86400000

# WeChat Configuration
wechat.miniapp.appid=wx43cfd09b9d13cda0
wechat.miniapp.secret=3c50638d8dfbc3cd2d2051d418662daf
wechat.miniapp.token=your-wechat-token
wechat.miniapp.aesKey=KOqJY1LFBd6n4Ihf1O3/rkvMLBpCnFBJ6TYWlYt2+zE=
wechat.miniapp.msgDataFormat=JSON

# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate=ERROR
logging.level.com.todaymurmur=DEBUG
logging.level.com.todaymurmur.api.security=DEBUG
logging.level.com.todaymurmur.api.controller=DEBUG
logging.level.com.todaymurmur.api.util.SecurityUtils=DEBUG

# Log file configuration
logging.file.name=logs/application.log
logging.file.max-size=10MB
logging.file.max-history=10
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

app.encryption.pepper=murmur
