package com.todaymurmur.api.dto;

import com.todaymurmur.api.model.User;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class JwtDTO {
    private String token;
    private String type = "Bearer";
    private Long id;
    private String username;
    private User user;

    public JwtDTO(String token, Long id, String username) {
        this.token = token;
        this.id = id;
        this.username = username;
    }
}
