package com.todaymurmur.api.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ApiResponse<T> {
    private static final int SUCCESS_CODE = 200;
    private static final String SUCCESS_MESSAGE = "操作成功";
    private static final int ERROR_CODE = 500;
    private static final String ERROR_MESSAGE = "服务器内部错误";

    private int code;
    private String message;
    private T data;

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    /**
     * Create an error response with message and no data
     *
     * @param <T> Type of data
     * @param message Error message
     * @return ApiResponse with error status
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(ERROR_CODE, message, null);
    }

    /**
     * Create an error response with message and data
     *
     * @param <T> Type of data
     * @param message Error message
     * @param data Error data
     * @return ApiResponse with error status
     */
    public static <T> ApiResponse<T> error(String message, T data) {
        return new ApiResponse<>(ERROR_CODE, message, data);
    }

    /**
     * Create a warning response with message and data
     *
     * @param <T> Type of data
     * @param message Warning message
     * @param data Warning data
     * @return ApiResponse with warning status
     */
    public static <T> ApiResponse<T> warning(String message, T data) {
        return new ApiResponse<>(ERROR_CODE, message, data);
    }
}
