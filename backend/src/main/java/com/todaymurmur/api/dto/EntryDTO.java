package com.todaymurmur.api.dto;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class EntryDTO {
    private Long id;
    private String notes;
    private LocalDateTime date;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private EmotionDto emotion;
    private CauseDto cause;

    @Data
    public static class EmotionDto {
        private Long id;
        private String label;
        private String icon;
        private String color;
        private Integer intensity;
    }

    @Data
    public static class CauseDto {
        private Long id;
        private String label;
    }
}
