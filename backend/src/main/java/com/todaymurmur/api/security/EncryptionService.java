package com.todaymurmur.api.security;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Service for encrypting and decrypting sensitive data using AES-256 encryption. Uses a combination of user credentials
 * and device fingerprint for enhanced security.
 */
@Service
public class EncryptionService {

    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int TAG_LENGTH_BIT = 128;
    private static final int IV_LENGTH_BYTE = 12;
    private static final int SALT_LENGTH_BYTE = 16;
    private static final int KEY_LENGTH_BIT = 256;

    @Value("${app.encryption.pepper}")
    private String pepper;

    /**
     * Encrypts plaintext using AES-256 encryption with GCM mode. Uses a combination of user credentials and device
     * fingerprint for key generation.
     *
     * @param plainText The text to encrypt
     * @return Base64 encoded encrypted string
     */
    public String encrypt(String plainText) {
        try {
            // Get current user's credentials and device fingerprint as part of the key
            String userSalt = getCurrentUserSalt();
            String deviceFingerprint = getDeviceFingerprint();

            // Generate random salt
            byte[] salt = new byte[SALT_LENGTH_BYTE];
            SecureRandom secureRandom = new SecureRandom();
            secureRandom.nextBytes(salt);

            // Generate key using both user credentials and device fingerprint
            SecretKey key = getAESKeyFromPassword((userSalt + deviceFingerprint).toCharArray(), salt);

            // Generate IV
            byte[] iv = new byte[IV_LENGTH_BYTE];
            secureRandom.nextBytes(iv);

            // Encrypt
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
            cipher.init(Cipher.ENCRYPT_MODE, key, parameterSpec);

            byte[] cipherText = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // Combine salt, iv, and cipherText
            ByteBuffer byteBuffer = ByteBuffer.allocate(salt.length + iv.length + cipherText.length);
            byteBuffer.put(salt);
            byteBuffer.put(iv);
            byteBuffer.put(cipherText);

            // Encode as Base64
            return Base64.getEncoder().encodeToString(byteBuffer.array());

        } catch (Exception e) {
            throw new RuntimeException("Error encrypting data", e);
        }
    }

    /**
     * Decrypts an encrypted string using AES-256 decryption with GCM mode. Uses the same combination of user
     * credentials and device fingerprint for key generation.
     *
     * @param encryptedText Base64 encoded encrypted string
     * @return Decrypted plaintext
     */
    public String decrypt(String encryptedText) {
        try {
            // Get current user's credentials and device fingerprint
            String userSalt = getCurrentUserSalt();
            String deviceFingerprint = getDeviceFingerprint();

            // Decode from Base64
            byte[] decoded = Base64.getDecoder().decode(encryptedText);

            // Extract salt, iv, and cipherText
            ByteBuffer byteBuffer = ByteBuffer.wrap(decoded);
            byte[] salt = new byte[SALT_LENGTH_BYTE];
            byteBuffer.get(salt);

            byte[] iv = new byte[IV_LENGTH_BYTE];
            byteBuffer.get(iv);

            byte[] cipherText = new byte[byteBuffer.remaining()];
            byteBuffer.get(cipherText);

            // Generate key using both user credentials and device fingerprint
            SecretKey key = getAESKeyFromPassword((userSalt + deviceFingerprint).toCharArray(), salt);

            // Decrypt
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
            cipher.init(Cipher.DECRYPT_MODE, key, parameterSpec);

            byte[] plainText = cipher.doFinal(cipherText);

            return new String(plainText, StandardCharsets.UTF_8);

        } catch (Exception e) {
            throw new RuntimeException("Error decrypting data", e);
        }
    }

    /**
     * Generates an AES key from a password and salt using PBKDF2WithHmacSHA256.
     *
     * @param password Password to use for key generation
     * @param salt Salt to use for key generation
     * @return SecretKey for AES encryption/decryption
     */
    private SecretKey getAESKeyFromPassword(char[] password, byte[] salt) throws Exception {
        // Combine user password with pepper for additional security
        char[] combinedPassword = (new String(password) + pepper).toCharArray();

        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        KeySpec spec = new PBEKeySpec(combinedPassword, salt, 65536, KEY_LENGTH_BIT);
        SecretKey secretKey = factory.generateSecret(spec);
        return new SecretKeySpec(secretKey.getEncoded(), "AES");
    }

    /**
     * Gets the current user's username to use as part of the encryption key.
     *
     * @return Current user's username
     */
    private String getCurrentUserSalt() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication.getName();
    }

    /**
     * Generates a device fingerprint based on request headers and client information. This adds an additional layer of
     * security by binding encrypted data to the device.
     *
     * @return Device fingerprint string
     */
    private String getDeviceFingerprint() {
        try {
            ServletRequestAttributes attr = (ServletRequestAttributes)RequestContextHolder.currentRequestAttributes();
            HttpServletRequest request = attr.getRequest();

            // Combine various request attributes to create a device fingerprint
            StringBuilder fingerprint = new StringBuilder();
            fingerprint.append(request.getHeader("User-Agent") != null ? request.getHeader("User-Agent") : "");
            fingerprint.append(request.getRemoteAddr() != null ? request.getRemoteAddr() : "");
            fingerprint
                .append(request.getHeader("Accept-Language") != null ? request.getHeader("Accept-Language") : "");

            // Add more attributes if needed for stronger device binding

            return fingerprint.toString();
        } catch (Exception e) {
            // Fallback to a default value if we can't get request attributes
            return "default-device-fingerprint";
        }
    }
}
