package com.todaymurmur.api.util;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EntryDTO {
    private Long entryId;
    private String notes;
    private LocalDateTime date;
    private Integer intensity;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // User info
    private Long userId;
    
    // Emotion info
    private Long emotionId;
    private String emotionLabel;
    private String emotionIcon;
    private String emotionColor;
    
    // Cause info
    private Long causeId;
    private String causeLabel;
}
