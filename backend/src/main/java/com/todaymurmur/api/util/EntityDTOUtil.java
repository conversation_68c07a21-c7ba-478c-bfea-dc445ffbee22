package com.todaymurmur.api.util;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.todaymurmur.api.model.Cause;
import com.todaymurmur.api.model.Emotion;
import com.todaymurmur.api.model.Entry;
import com.todaymurmur.api.model.User;

/**
 * Utility class to convert entities to DTOs to avoid circular references when serializing to JSON.
 */
@Component
public class EntityDTOUtil {

    /**
     * Convert a User entity to a simplified DTO without circular references
     */
    public UserDTO toUserDTO(User user) {
        if (user == null) {
            return null;
        }

        UserDTO dto = new UserDTO();
        dto.setUserId(user.getUserId());
        dto.setUsername(user.getUsername());
        dto.setNickname(user.getNickname());
        dto.setAvatarUrl(user.getAvatarUrl());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setLastLoginTime(user.getLastLoginTime());

        return dto;
    }

    /**
     * Convert an Emotion entity to a simplified DTO without circular references
     */
    public EmotionDTO toEmotionDTO(Emotion emotion) {
        if (emotion == null) {
            return null;
        }

        EmotionDTO dto = new EmotionDTO();
        dto.setEmotionId(emotion.getEmotionId());
        dto.setEmotionLabel(emotion.getEmotionLabel());
        dto.setEmotionIcon(emotion.getEmotionIcon());
        dto.setEmotionColor(emotion.getEmotionColor());
        dto.setIsCustom(emotion.getIsCustom());
        dto.setIsDeleted(emotion.getIsDeleted());
        dto.setCreatedAt(emotion.getCreatedAt());
        dto.setUpdatedAt(emotion.getUpdatedAt());

        if (emotion.getUser() != null) {
            dto.setUserId(emotion.getUser().getUserId());
        }

        return dto;
    }

    /**
     * Convert a Cause entity to a simplified DTO without circular references
     */
    public CauseDTO toCauseDTO(Cause cause) {
        if (cause == null) {
            return null;
        }

        CauseDTO dto = new CauseDTO();
        dto.setCauseId(cause.getCauseId());
        dto.setCauseLabel(cause.getCauseLabel());
        dto.setCauseCategory(cause.getCauseCategory());
        dto.setIsCustom(cause.getIsCustom());
        dto.setIsDeleted(cause.getIsDeleted());
        dto.setCreatedAt(cause.getCreatedAt());
        dto.setUpdatedAt(cause.getUpdatedAt());

        if (cause.getUser() != null) {
            dto.setUserId(cause.getUser().getUserId());
        }

        return dto;
    }

    /**
     * Convert an Entry entity to a simplified DTO without circular references
     */
    public EntryDTO toEntryDTO(Entry entry) {
        if (entry == null) {
            return null;
        }

        EntryDTO dto = new EntryDTO();
        dto.setEntryId(entry.getEntryId());
        dto.setNotes(entry.getNotes());
        dto.setDate(entry.getDate());
        dto.setIntensity(entry.getIntensity());
        dto.setCreatedAt(entry.getCreatedAt());
        dto.setUpdatedAt(entry.getUpdatedAt());

        if (entry.getUser() != null) {
            dto.setUserId(entry.getUser().getUserId());
        }

        if (entry.getEmotion() != null) {
            dto.setEmotionId(entry.getEmotion().getEmotionId());
            dto.setEmotionLabel(entry.getEmotion().getEmotionLabel());
            dto.setEmotionIcon(entry.getEmotion().getEmotionIcon());
            dto.setEmotionColor(entry.getEmotion().getEmotionColor());
        }

        if (entry.getCause() != null) {
            dto.setCauseId(entry.getCause().getCauseId());
            dto.setCauseLabel(entry.getCause().getCauseLabel());
        }

        return dto;
    }

    /**
     * Convert a list of Entry entities to a list of DTOs
     */
    public List<EntryDTO> toEntryDTOList(List<Entry> entries) {
        if (entries == null) {
            return null;
        }

        return entries.stream().map(this::toEntryDTO).collect(Collectors.toList());
    }

    /**
     * Convert a list of Emotion entities to a list of DTOs
     */
    public List<EmotionDTO> toEmotionDTOList(List<Emotion> emotions) {
        if (emotions == null) {
            return null;
        }

        return emotions.stream().map(this::toEmotionDTO).collect(Collectors.toList());
    }

    /**
     * Convert a list of Cause entities to a list of DTOs
     */
    public List<CauseDTO> toCauseDTOList(List<Cause> causes) {
        if (causes == null) {
            return null;
        }

        return causes.stream().map(this::toCauseDTO).collect(Collectors.toList());
    }
}
