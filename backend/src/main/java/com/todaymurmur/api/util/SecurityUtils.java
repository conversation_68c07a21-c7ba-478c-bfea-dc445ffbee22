package com.todaymurmur.api.util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.todaymurmur.api.exception.UserNotFoundException;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.UserRepository;

/**
 * Utility class for security-related operations.
 */
public class SecurityUtils {
    private static final Logger logger = LoggerFactory.getLogger(SecurityUtils.class);

    // Simple in-memory cache for user lookups
    private static final Map<String, CachedUser> userCache = new ConcurrentHashMap<>();

    // Cache expiration time in milliseconds (5 minutes)
    private static final long CACHE_EXPIRATION_MS = TimeUnit.MINUTES.toMillis(5);

    /**
     * Get the current authenticated user.
     *
     * @param userRepository The user repository to use for lookup
     * @return The current user
     * @throws RuntimeException if the user is not found
     */
    public static User getCurrentUser(UserRepository userRepository) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null) {
            logger.error("No authentication found in security context");
            throw new UserNotFoundException("User not authenticated");
        }

        String identifier = authentication.getName();
        logger.debug("Getting current user with identifier: {}", identifier);

        // Check cache first
        CachedUser cachedUser = userCache.get(identifier);
        if (cachedUser != null && !cachedUser.isExpired()) {
            logger.debug("User found in cache: {}", identifier);
            return cachedUser.getUser();
        }

        // Cache miss or expired, fetch from database
        try {
            // Try to find by openId first (for WeChat users), then by username
            User user = userRepository.findByOpenId(identifier)
                .orElseGet(() -> userRepository.findByUsername(identifier).orElseThrow(() -> {
                    logger.error("User not found with identifier: {}", identifier);
                    return new UserNotFoundException("User not found with identifier: " + identifier);
                }));

            // Cache the result
            userCache.put(identifier, new CachedUser(user));
            logger.debug("User cached: {}", identifier);

            return user;
        } catch (Exception e) {
            logger.error("Error finding user with identifier: {}", identifier, e);
            throw e;
        }
    }

    /**
     * Clear the user cache.
     */
    public static void clearUserCache() {
        userCache.clear();
        logger.debug("User cache cleared");
    }

    /**
     * Remove a specific user from the cache.
     *
     * @param identifier The user identifier (openId or username)
     */
    public static void removeFromCache(String identifier) {
        userCache.remove(identifier);
        logger.debug("User removed from cache: {}", identifier);
    }

    /**
     * Inner class to represent a cached user with expiration.
     */
    private static class CachedUser {
        private final User user;
        private final long expirationTime;

        public CachedUser(User user) {
            this.user = user;
            this.expirationTime = System.currentTimeMillis() + CACHE_EXPIRATION_MS;
        }

        public User getUser() {
            return user;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }
    }
}
