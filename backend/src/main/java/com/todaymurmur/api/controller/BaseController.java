package com.todaymurmur.api.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.todaymurmur.api.dto.response.ApiResponse;
import com.todaymurmur.api.exception.UserNotFoundException;
import com.todaymurmur.api.model.User;
import com.todaymurmur.api.repository.UserRepository;

/**
 * Base controller class that provides common functionality for all controllers. Implements standardized response
 * methods and common utilities.
 */
public abstract class BaseController {
    private static final int SUCCESS_CODE = 200;
    private static final String SUCCESS_MESSAGE = "操作成功";
    private static final int ERROR_CODE = 500;
    private static final String ERROR_MESSAGE = "服务器内部错误";

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(SUCCESS_CODE, SUCCESS_MESSAGE, data);
    }

    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(SUCCESS_CODE, SUCCESS_MESSAGE, null);
    }

    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(SUCCESS_CODE, message, data);
    }

    public static <T> ApiResponse<T> error() {
        return new ApiResponse<>(ERROR_CODE, ERROR_MESSAGE, null);
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(ERROR_CODE, message, null);
    }

    /**
     * Get the current authenticated user
     *
     * @param userRepository User repository to fetch user details
     * @return Current authenticated user
     * @throws UserNotFoundException if user is not found
     */
    protected User getCurrentUser(UserRepository userRepository) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();

        return userRepository.findByUsername(username)
            .orElseThrow(() -> new UserNotFoundException("User not found: " + username));
    }
}
