package com.todaymurmur.api.repository;

import com.todaymurmur.api.model.Emotion;
import com.todaymurmur.api.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmotionRepository extends JpaRepository<Emotion, Long> {
    List<Emotion> findByIsCustomFalseAndIsDeletedFalse();
    
    List<Emotion> findByUserAndIsCustomTrueAndIsDeletedFalse(User user);
    
    @Query("SELECT e FROM Emotion e WHERE e.isCustom = false OR (e.isCustom = true AND e.user = ?1) AND e.isDeleted = false")
    List<Emotion> findAllAvailableForUser(User user);
    
    Optional<Emotion> findByEmotionIdAndUser(Long emotionId, User user);
    
    Optional<Emotion> findByEmotionIdAndIsCustomFalseAndIsDeletedFalse(Long emotionId);
}
