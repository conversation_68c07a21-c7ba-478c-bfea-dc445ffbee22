package com.todaymurmur.api.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.todaymurmur.api.model.Entry;
import com.todaymurmur.api.model.User;

@Repository
public interface EntryRepository extends JpaRepository<Entry, Long> {
    Page<Entry> findByUserOrderByDateDesc(User user, Pageable pageable);

    List<Entry> findByUserAndDateBetweenOrderByDateDesc(User user, LocalDateTime startDate, LocalDateTime endDate);

    Optional<Entry> findByEntryIdAndUser(Long entryId, User user);
}
