<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="93f0cdb8-a23f-4882-9c70-17f7707bd0e6" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="FormatOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="alwaysUpdateSnapshots" value="true" />
        <option name="customMavenHome" value="$USER_HOME$/java_tools/apache-maven-3.6.0" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="$USER_HOME$/java_tools/apache-maven-3.6.0/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="openjdk1.8" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="openjdk1.8" />
  </component>
  <component name="OptimizeOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2wj1e9e898rdCqtktyfgs7zDl0n" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.TodayMurmurApplication.executor&quot;: &quot;Run&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Desktop/work-NJ/SELF/今日碎念/codes/backend&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="TodayMurmurApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="today-murmur-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.todaymurmur.api.TodayMurmurApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SvnConfiguration">
    <configuration>$USER_HOME$/.subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="93f0cdb8-a23f-4882-9c70-17f7707bd0e6" name="Changes" comment="" />
      <created>1745560012011</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745560012011</updated>
      <workItem from="1746540263197" duration="8476000" />
      <workItem from="1746611098119" duration="2667000" />
      <workItem from="1746686448997" duration="3752000" />
      <workItem from="1746755445993" duration="9865000" />
      <workItem from="1746782078178" duration="16541000" />
      <workItem from="1747137246477" duration="2568000" />
      <workItem from="1747207529444" duration="8427000" />
      <workItem from="1747272774452" duration="267000" />
      <workItem from="1747273058830" duration="598000" />
      <workItem from="1747274311275" duration="598000" />
      <workItem from="1747275375263" duration="31151000" />
      <workItem from="1747722105018" duration="2676000" />
      <workItem from="1747808241122" duration="90000" />
      <workItem from="1747808350514" duration="9533000" />
      <workItem from="1747964128628" duration="1000" />
      <workItem from="1748251440983" duration="1281000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="true" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>