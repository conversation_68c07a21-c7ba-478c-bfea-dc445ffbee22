<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id>CSS</id>
          </State>
          <State>
            <id>Class structureJava</id>
          </State>
          <State>
            <id>Code maturityJava</id>
          </State>
          <State>
            <id>Code quality toolsCSS</id>
          </State>
          <State>
            <id>Code quality toolsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>EmbeddedPerformanceJava</id>
          </State>
          <State>
            <id>Error handlingJava</id>
          </State>
          <State>
            <id>General</id>
          </State>
          <State>
            <id>HTTP Client</id>
          </State>
          <State>
            <id>Internationalization</id>
          </State>
          <State>
            <id>JUnitJava</id>
          </State>
          <State>
            <id>JVM languages</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Java 5Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 7Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 8Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java 9Java language level migration aidsJava</id>
          </State>
          <State>
            <id>Java language level migration aidsJava</id>
          </State>
          <State>
            <id>JavaScript and TypeScript</id>
          </State>
          <State>
            <id>JavadocJava</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>LoggingJava</id>
          </State>
          <State>
            <id>MemoryJava</id>
          </State>
          <State>
            <id>MigrationKotlin</id>
          </State>
          <State>
            <id>Numeric issuesJava</id>
          </State>
          <State>
            <id>PerformanceJava</id>
          </State>
          <State>
            <id>Probable bugsJava</id>
          </State>
          <State>
            <id>Resource managementJava</id>
          </State>
          <State>
            <id>SQL</id>
          </State>
          <State>
            <id>Shell script</id>
          </State>
          <State>
            <id>Spring</id>
          </State>
          <State>
            <id>Spring AOPSpring</id>
          </State>
          <State>
            <id>TestNGJava</id>
          </State>
          <State>
            <id>Threading issuesJava</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>User defined</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_11" project-jdk-name="homebrew-11" project-jdk-type="JavaSDK" />
</project>