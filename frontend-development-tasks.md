# Today's Murmur 前端开发任务分解文档

## 项目概述
基于 Taro4 + Vue3 + NutUI 技术栈的情绪记录小程序前端开发任务分解。

## 技术栈确认
- **框架**: Taro4 + Vue3 + TypeScript
- **UI组件库**: NutUI 4.x for Vue3
- **状态管理**: Pinia
- **图标系统**:
  - 情绪图标：自定义IconFont (Fluent Design风格)
  - 功能图标：Font Awesome (免费版) + @fortawesome/vue-fontawesome
- **样式**: Sass/SCSS

## 任务分类与优先级

### 🔴 P0 - 核心基础设施 (第1周)

#### T001 - 项目架构搭建
**描述**: 完善项目基础架构和配置
**任务内容**:
- 配置路由系统和页面结构
- 设置Pinia状态管理
- 配置自定义IconFont和Font Awesome
- 建立统一的样式规范和主题配置

**验收标准**:
- [ ] 路由配置完成，支持页面跳转
- [ ] Pinia store正常工作
- [ ] 图标系统可正常使用
- [ ] 主题色彩和字体规范生效

**依赖**: 无
**预估工时**: 2天

#### T002 - 微信授权登录
**描述**: 实现微信小程序授权登录功能
**任务内容**:
- 集成微信登录API
- 实现用户信息获取和存储
- 建立登录状态管理
- 处理登录失败

**验收标准**:
- [ ] 用户可通过微信授权登录
- [ ] 登录状态持久化存储
- [ ] 登录失败有适当提示
- [ ] 支持自动登录

**依赖**: T001
**预估工时**: 3天

### 🟡 P1 - 核心页面开发 (第2-3周)

#### T003 - 记录页 (首页) 开发
**描述**: 开发应用主页面，包含今日碎语快照和快速记录入口
**任务内容**:
- 实现顶部栏布局 (标题 + 用户头像)
- 开发焦点卡片组件 (今日最新碎语快照)
- 实现浮动操作按钮
- 开发底部导航栏
- 实现下拉刷新功能

**验收标准**:
- [ ] 页面布局符合设计规范
- [ ] 焦点卡片正确显示今日记录或引导语
- [ ] 浮动按钮跳转至记录页
- [ ] 底部导航正常工作
- [ ] 下拉刷新有加载动画

**依赖**: T001, T002
**预估工时**: 4天

#### T004 - 情绪记录页开发
**描述**: 开发情绪记录核心功能页面
**任务内容**:
- 实现情绪选择器 (3x3网格)
- 开发原因分类选择组件
- 实现笔记输入区域
- 开发提交按钮和验证逻辑
- 实现语音输入和Emoji选择器

**验收标准**:
- [ ] 情绪选择器支持单选，有视觉反馈
- [ ] 原因分类支持二级联动选择
- [ ] 文本输入框自适应高度，有字数统计
- [ ] 必填项验证正确
- [ ] 保存成功后返回首页并刷新

**依赖**: T001, T002, T003
**预估工时**: 5天

### 🟢 P2 - 数据展示页面 (第4周)

#### T005 - 图表页开发
**描述**: 开发数据可视化页面，不使用复杂图表库
**任务内容**:
- 实现时间筛选栏和视图切换
- 开发情绪时间轴组件
- 实现情绪统计列表
- 开发原因分析组件
- 实现洞察总结卡片

**验收标准**:
- [ ] 时间筛选器正常工作
- [ ] 情绪时间轴正确显示记录点
- [ ] 统计列表按频次排序
- [ ] 原因分析支持类别/具体原因切换
- [ ] 洞察总结动态生成

**依赖**: T001, T002, T004
**预估工时**: 6天

#### T006 - 日记页开发
**描述**: 开发历史记录查看和管理页面
**任务内容**:
- 实现筛选栏 (情绪筛选 + 日期筛选)
- 开发日记条目列表
- 实现详情弹窗
- 开发左滑编辑/删除功能
- 实现空状态页面

**验收标准**:
- [ ] 筛选功能正常工作
- [ ] 列表正确显示记录卡片
- [ ] 详情弹窗显示完整信息
- [ ] 左滑操作流畅，删除需确认
- [ ] 空状态有引导操作

**依赖**: T001, T002, T004
**预估工时**: 4天

### 🔵 P3 - 用户功能页面 (第5周)

#### T007 - 个人中心页开发
**描述**: 开发用户个人信息和设置页面
**任务内容**:
- 实现用户信息区域
- 开发功能入口列表
- 实现退出登录功能
- 添加版本信息和彩蛋

**验收标准**:
- [ ] 用户信息正确显示
- [ ] 功能入口有点击反馈
- [ ] 退出登录需要确认
- [ ] 版本号点击有彩蛋效果

**依赖**: T001, T002
**预估工时**: 2天

### 🟣 P4 - 高级功能 (第6周)

#### T008 - 自定义情绪库
**描述**: 开发用户自定义情绪功能
**任务内容**:
- 实现情绪管理页面
- 开发添加/编辑情绪功能
- 实现情绪图标选择器
- 支持情绪颜色自定义

**验收标准**:
- [ ] 可查看系统和自定义情绪
- [ ] 支持添加新情绪
- [ ] 图标和颜色选择器正常工作
- [ ] 自定义情绪在记录页正常显示

**依赖**: T004
**预估工时**: 4天

#### T009 - 帮助中心
**描述**: 开发应用使用指南页面
**任务内容**:
- 设计帮助内容结构
- 实现常见问题页面
- 开发使用教程
- 添加联系方式

**验收标准**:
- [ ] 帮助内容结构清晰
- [ ] 常见问题可展开/收起
- [ ] 使用教程有图文说明
- [ ] 联系方式可点击

**依赖**: T007
**预估工时**: 2天

### 🟤 P5 - 性能优化与完善 (第7周)

#### T010 - 性能优化
**描述**: 优化应用性能和用户体验
**任务内容**:
- 实现图片懒加载
- 优化列表渲染性能
- 添加骨架屏加载
- 优化包体积

**验收标准**:
- [ ] 页面加载时间 < 3秒
- [ ] 长列表滚动流畅
- [ ] 加载状态有骨架屏
- [ ] 包体积控制在合理范围

**依赖**: 所有页面开发完成
**预估工时**: 3天

#### T011 - 错误处理与边界情况
**描述**: 完善错误处理和边界情况
**任务内容**:
- 实现全局错误处理
- 添加网络异常处理
- 完善表单验证
- 处理数据为空的情况

**验收标准**:
- [ ] 网络错误有友好提示
- [ ] 表单验证覆盖所有场景
- [ ] 空数据状态处理完善
- [ ] 异常情况不会导致崩溃

**依赖**: 所有功能开发完成
**预估工时**: 2天

## 组件复用规划

### 通用组件
- **EmotionSelector**: 情绪选择器组件
- **CauseSelector**: 原因选择器组件
- **EmotionCard**: 情绪记录卡片组件
- **StatisticsList**: 统计列表组件
- **TimelineChart**: 时间轴图表组件
- **LoadingSpinner**: 加载动画组件
- **EmptyState**: 空状态组件

### 业务组件
- **TodaySnapshot**: 今日快照组件
- **RecordForm**: 记录表单组件
- **FilterBar**: 筛选栏组件
- **InsightCard**: 洞察卡片组件

## 开发注意事项

1. **设计规范遵循**: 严格按照需求文档中的视觉规范实现
2. **响应式适配**: 确保在不同尺寸设备上的显示效果
3. **无障碍访问**: 添加适当的aria标签和语义化标签
4. **性能考虑**: 合理使用虚拟列表和懒加载
5. **错误边界**: 每个页面都要有错误边界处理
6. **测试覆盖**: 核心功能需要编写单元测试

## 风险评估

- **高风险**: 微信授权登录集成 (T002)
- **中风险**: 自定义图表实现 (T005)
- **低风险**: 基础页面开发 (T003, T006, T007)

## API接口规划

### 用户相关接口
- `POST /api/auth/wechat-login` - 微信授权登录
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息

### 情绪记录接口
- `POST /api/entries` - 创建情绪记录
- `GET /api/entries` - 获取情绪记录列表
- `PUT /api/entries/:id` - 更新情绪记录
- `DELETE /api/entries/:id` - 删除情绪记录

### 基础数据接口
- `GET /api/emotions` - 获取情绪列表
- `POST /api/emotions` - 创建自定义情绪
- `GET /api/causes` - 获取原因列表
- `GET /api/statistics` - 获取统计数据

## 状态管理设计

### Store模块划分
```typescript
// stores/user.ts - 用户状态
interface UserState {
  userInfo: UserInfo | null
  isLoggedIn: boolean
  token: string
}

// stores/emotion.ts - 情绪数据
interface EmotionState {
  emotions: Emotion[]
  causes: Cause[]
  customEmotions: Emotion[]
}

// stores/entry.ts - 记录数据
interface EntryState {
  entries: Entry[]
  todayEntries: Entry[]
  statistics: Statistics
}
```

## 关键技术实现点

### 1. 自定义IconFont集成
```scss
// 情绪图标样式
@font-face {
  font-family: 'emotion-icons';
  src: url('./assets/fonts/emotion-icons.woff2') format('woff2');
}

.emotion-icon {
  font-family: 'emotion-icons';
  font-style: normal;
  font-weight: normal;
}
```

### 2. 图表组件实现
```vue
<!-- 使用CSS实现简化图表 -->
<template>
  <div class="timeline-chart">
    <div class="timeline-axis">
      <div
        v-for="entry in entries"
        :key="entry.id"
        class="timeline-point"
        :style="{ left: getTimePosition(entry.date) }"
      >
        <i :class="entry.emotion.emotionIcon"
           :style="{ color: entry.emotion.emotionColor }"></i>
      </div>
    </div>
  </div>
</template>
```

### 3. 手势操作实现
```typescript
// 左滑删除功能
import { useTouch } from '@/composables/useTouch'

const { onTouchStart, onTouchMove, onTouchEnd } = useTouch({
  onSwipeLeft: () => showActions.value = true,
  onSwipeRight: () => showActions.value = false
})
```

## 测试策略

### 单元测试覆盖
- [ ] 情绪选择器组件测试
- [ ] 记录表单验证测试
- [ ] 统计数据计算测试
- [ ] 状态管理测试

### 集成测试
- [ ] 登录流程测试
- [ ] 记录创建流程测试
- [ ] 数据同步测试

### E2E测试
- [ ] 完整用户流程测试
- [ ] 跨页面导航测试

## 部署配置

### 微信小程序配置
```json
// project.config.json
{
  "appid": "your-app-id",
  "projectname": "todays-murmur",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true
  }
}
```

### 构建优化
- 启用Tree Shaking
- 图片资源压缩
- 代码分割优化
- 依赖包分析

## 总预估工时
**总计**: 约 37 工作日 (7-8周)

## 里程碑计划

- **Week 1**: 基础架构 + 登录功能
- **Week 2-3**: 核心记录功能
- **Week 4**: 数据展示功能
- **Week 5**: 用户功能完善
- **Week 6**: 高级功能开发
- **Week 7**: 优化测试上线

## 后续迭代规划

### V1.1 版本
- 数据导出功能
- 情绪提醒设置
- 主题切换功能

### V1.2 版本
- 社交分享功能
- 情绪分析报告
- 数据备份恢复
